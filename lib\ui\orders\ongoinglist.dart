import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:math' hide log;
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:db_eats/bloc/order_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/data/models/booking/bookinglistmodel.dart';
import 'package:db_eats/server/serverhelper.dart';
import 'package:db_eats/ui/cart/yourcart.dart';
import 'package:db_eats/ui/cart/cart-subitem.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:db_eats/ui/orders/ongoing_mealplandetail.dart';
import 'package:db_eats/ui/rating/chef_review_page.dart';
import 'package:db_eats/ui/rating/driver_rating_page.dart';
import 'package:db_eats/ui/support/customer_support.dart';
import 'package:db_eats/utils/dotteddivider.dart';
import 'package:db_eats/widgets/floatingactionbutton.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart' hide Marker;

class Ongoinglist extends StatefulWidget {
  const Ongoinglist({super.key});

  @override
  State<Ongoinglist> createState() => _OngoinglistState();
}

class _OngoinglistState extends State<Ongoinglist> {
  int? _lastLoadedOrderId;
  int _selectedTabIndex = 0;
  final List<String> _tabs = ['Ongoing', 'Meal Plans', 'Past Orders'];
  final ScrollController _ongoingScrollController = ScrollController();
  final ScrollController _pastScrollController = ScrollController();

  late PageController _pageController;

  Map<String, dynamic>? _selectedOrder;
  int? _selectedOrderId;
  String? _selectedOrderStatus;

  int _ongoingCurrentPage = 1;
  int _ongoingTotalPages = 1;
  int _pastCurrentPage = 1;
  int _pastTotalPages = 1;
  bool _isLoadingMoreOngoing = false;
  bool _isLoadingMorePast = false;
  List<Bookings> _ongoingBookings = [];
  List<Bookings> _pastBookings = [];
  int? _lastReorderChefId;

  @override
  void initState() {
    super.initState();
    context.read<OrderBloc>().add(
          BookingListEvent(
            _tabs[_selectedTabIndex].toLowerCase() == 'ongoing'
                ? 'ongoing'
                : _tabs[_selectedTabIndex].toLowerCase() == 'past orders'
                    ? 'past'
                    : 'past',
            page:
                _selectedTabIndex == 0 ? _ongoingCurrentPage : _pastCurrentPage,
          ),
        );
    _pageController = PageController(initialPage: _selectedTabIndex);
    _ongoingScrollController.addListener(_onOngoingScroll);
    _pastScrollController.addListener(_onPastScroll);
    _startLocationRefreshTimer();
  }

  void _onOngoingScroll() {
    print(
        'Scroll position: ${_ongoingScrollController.position.pixels}, Max: ${_ongoingScrollController.position.maxScrollExtent}');
    if (_ongoingScrollController.position.pixels >=
        _ongoingScrollController.position.maxScrollExtent - 100) {
      print(
          'Triggering pagination: Current page $_ongoingCurrentPage, Total pages $_ongoingTotalPages, Loading: $_isLoadingMoreOngoing');
      if (_ongoingCurrentPage < _ongoingTotalPages && !_isLoadingMoreOngoing) {
        setState(() {
          _isLoadingMoreOngoing = true;
          _ongoingCurrentPage++;
        });
        context.read<OrderBloc>().add(
              BookingListEvent('ongoing', page: _ongoingCurrentPage),
            );
      }
    }
  }

  void _onPastScroll() {
    if (_pastScrollController.position.pixels >=
        _pastScrollController.position.maxScrollExtent - 100) {
      if (_pastCurrentPage < _pastTotalPages && !_isLoadingMorePast) {
        setState(() {
          _isLoadingMorePast = true;
          _pastCurrentPage++;
        });
        context.read<OrderBloc>().add(
              BookingListEvent('past', page: _pastCurrentPage),
            );
      }
    }
  }

  Timer? _locationRefreshTimer;

  void _startLocationRefreshTimer() {
    _locationRefreshTimer?.cancel();

    _locationRefreshTimer = Timer.periodic(const Duration(seconds: 8), (timer) {
      if (_selectedOrderId != null && mounted) {
        context
            .read<OrderBloc>()
            .add(ViewOrederdetailsEvent2(_selectedOrderId!));

        if (_mapController != null &&
            Initializer.viewOrderDetailsModel.data?.assignedDriver
                    ?.driverCurrentLocation?.location?.coordinates !=
                null) {
          final driverCoords = Initializer.viewOrderDetailsModel.data!
              .assignedDriver!.driverCurrentLocation!.location!.coordinates!;
          if (driverCoords.length >= 2) {
            // Move camera to new driver position
            // _mapController!.animateCamera(
            //   CameraUpdate.newLatLng(LatLng(driverCoords[1], driverCoords[0])),
            // );

            setState(() {});
          }
        }
      }
    });
  }

  GoogleMapController? _mapController;
  Set<Polyline> _polylines = {};
  Uint8List? deliveryIcon;
  Widget? _expandedOrderViewCache;
  int? _expandedOrderViewCacheId;
  bool _initialMapBoundsSet = false;
  LatLng? _lastDriverLocation;

  void deliveryicon() async {
    deliveryIcon =
        await getBytesFromAsset('assets/icons/customer_icon.png', 50);
  }

  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  void _updateMapBounds() {
    final orderData = Initializer.viewOrderDetailsModel.data!;

    if (_mapController == null) return;

    List<LatLng> points = [];

    if (orderData.chef?.location?.coordinates != null &&
        orderData.chef!.location!.coordinates!.length >= 2) {
      points.add(LatLng(
        orderData.chef!.location!.coordinates![1],
        orderData.chef!.location!.coordinates![0],
      ));
    }

    if (orderData.address?.location?.coordinates != null &&
        orderData.address!.location!.coordinates!.length >= 2) {
      points.add(LatLng(
        orderData.address!.location!.coordinates![1],
        orderData.address!.location!.coordinates![0],
      ));
    }

    if (orderData.assignedDriver != null &&
        orderData.assignedDriver!.driverCurrentLocation != null &&
        orderData.assignedDriver!.driverCurrentLocation!.location != null &&
        orderData
                .assignedDriver!.driverCurrentLocation!.location!.coordinates !=
            null &&
        orderData.assignedDriver!.driverCurrentLocation!.location!.coordinates!
                .length >=
            2) {
      points.add(LatLng(
        orderData
            .assignedDriver!.driverCurrentLocation!.location!.coordinates![1],
        orderData
            .assignedDriver!.driverCurrentLocation!.location!.coordinates![0],
      ));
    }

    if (points.length >= 2) {
      LatLngBounds bounds = _calculateBoundsFromPoints(points);
      if (_mapController != null &&
          _selectedOrderId != null &&
          !_initialMapBoundsSet) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngBounds(bounds, 50),
        );
        _initialMapBoundsSet = true;
      }
    } else if (points.length == 1) {
      if (_mapController != null &&
          _selectedOrderId != null &&
          !_initialMapBoundsSet) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLng(points[0]),
        );
        _initialMapBoundsSet = true;
      }
    }
  }

  LatLngBounds _calculateBoundsFromPoints(List<LatLng> points) {
    double? minLat, maxLat, minLng, maxLng;

    for (LatLng point in points) {
      minLat = minLat == null ? point.latitude : min(minLat, point.latitude);
      maxLat = maxLat == null ? point.latitude : max(maxLat, point.latitude);
      minLng = minLng == null ? point.longitude : min(minLng, point.longitude);
      maxLng = maxLng == null ? point.longitude : max(maxLng, point.longitude);
    }

    return LatLngBounds(
      southwest: LatLng(minLat!, minLng!),
      northeast: LatLng(maxLat!, maxLng!),
    );
  }

  Future<List<LatLng>> _getRouteCoordinates(
      LatLng origin, LatLng destination) async {
    const String apiKey = 'AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY';
    final String url =
        'https://maps.googleapis.com/maps/api/directions/json?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&key=$apiKey';

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          final String encodedPolyline =
              data['routes'][0]['overview_polyline']['points'];
          return _decodePolyline(encodedPolyline);
        } else {
          log('Directions API error: ${data['status']}');
          return [];
        }
      } else {
        log('Failed to fetch route: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      log('Error fetching route: $e');
      return [];
    }
  }

  List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> polyline = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      polyline.add(LatLng(lat / 1e5, lng / 1e5));
    }
    return polyline;
  }

  // Calculate distance between two LatLng points (simple approximation)
  double _calculateDistance(LatLng point1, LatLng point2) {
    double deltaLat = point1.latitude - point2.latitude;
    double deltaLng = point1.longitude - point2.longitude;
    return sqrt(deltaLat * deltaLat + deltaLng * deltaLng);
  }

  @override
  void dispose() {
    _locationRefreshTimer?.cancel();
    _pageController.dispose();
    _mapController?.dispose();
    _ongoingScrollController.dispose();
    _pastScrollController.dispose();
    super.dispose();
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  // Add responsive helper methods
  double getResponsiveSize(BuildContext context,
      {double small = 12,
      double medium = 16,
      double large = 20,
      double xlarge = 24}) {
    final width = MediaQuery.of(context).size.width;
    if (width < 360) return small;
    if (width < 600) return medium;
    if (width < 900) return large;
    return xlarge;
  }

  EdgeInsets getResponsivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final isLandscape = width > height;

    if (width < 360) {
      return EdgeInsets.all(width * 0.03);
    } else if (width < 600) {
      return EdgeInsets.all(width * 0.04);
    } else {
      return EdgeInsets.all(isLandscape ? width * 0.03 : width * 0.05);
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context,
        small: 12, medium: 14, large: 16, xlarge: 18);
    final itemSpacing = size.height * 0.013;

    return PopScope(
      canPop: _selectedOrder == null,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && _selectedOrder != null) {
          // Handle back gesture when expanded order view is open
          _closeExpandedOrderView();
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              InkWell(
                onTap: () {
                  // Navigator.push(
                  //   context,
                  //   MaterialPageRoute(
                  //     builder: (context) => Orderconfiemationmain(),
                  //   ),
                  // );
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: size.width * 0.05,
                    vertical: size.height * 0.015,
                  ),
                  child: Text(
                    'My Orders',
                    style: TextStyle(
                      fontSize: twenty,
                      fontWeight: FontWeight.w700,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
              ),
              // Tabs
              SizedBox(
                height: size.height * 0.05,
                child: Row(
                  children: List.generate(_tabs.length, (index) {
                    return Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedOrder = null;
                            _selectedTabIndex = index;
                            _pageController.animateToPage(
                              index,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          });
                          if (index == 1) {
                            context.read<OrderBloc>().add(MealPlanListEvent());
                          }
                        },
                        child: Container(
                          margin: EdgeInsets.symmetric(
                              horizontal: size.width * 0.001),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: _selectedTabIndex == index
                                    ? const Color(0xFFFFBE16)
                                    : Colors.transparent,
                                width: 2,
                              ),
                            ),
                          ),
                          child: Text(
                            _tabs[index],
                            style: TextStyle(
                              color: _selectedTabIndex == index
                                  ? const Color(0xFF1F2122)
                                  : const Color(0xFF66696D),
                              fontWeight: FontWeight.w600,
                              fontSize: baseTextSize,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              ),
              SizedBox(height: itemSpacing),
              // Content
              Expanded(
                child: Stack(
                  children: [
                    IgnorePointer(
                      ignoring: _selectedOrder != null,
                      child: PageView(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _selectedTabIndex = index;
                            _selectedOrder = null;
                            if (index == 0) {
                              _ongoingCurrentPage = 1;
                              _ongoingBookings = [];
                            } else if (index == 2) {
                              _pastCurrentPage = 1;
                              _pastBookings = [];
                            }
                          });
                          if (index == 1) {
                            // Meal Plans tab
                            context.read<OrderBloc>().add(MealPlanListEvent());
                          } else if (index == 2) {
                            // Past Orders tab
                            context.read<OrderBloc>().add(BookingListEvent(
                                'past',
                                page: _pastCurrentPage));
                          } else {
                            // Ongoing Orders tab
                            context.read<OrderBloc>().add(BookingListEvent(
                                'ongoing',
                                page: _ongoingCurrentPage));
                          }
                        },
                        children: [
                          _buildOngoingOrders(),
                          _buildMealPlans(),
                          _buildPastOrders(),
                        ],
                      ),
                    ),
                    if (_selectedOrder != null)
                      Container(
                        color: const Color(0xFFF6F3EC),
                        child: _expandedOrderViewCache ??
                            _buildExpandedOrderView(_selectedOrderId!),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: CartFloatingActionButton(
          itemCount: Initializer.cartCount ?? 0,
          onPressed: _openCart,
        ),
      ),
    );
  }

  void _closeExpandedOrderView() {
    _locationRefreshTimer?.cancel();
    _locationRefreshTimer = null;

    setState(() {
      _selectedOrder = null;
      _polylines = {};
      _expandedOrderViewCache = null;
      _selectedOrderId = null;
      _lastDriverLocation = null;
      _initialMapBoundsSet = false;
    });
  }

  void _openCart() {
    print('Opening cart');
    Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CartPage(),
        ));
  }

  String _formatTimeOnly(String time) {
    try {
      final parts = time.split(':');
      int hour = int.parse(parts[0]);
      final minutes = parts.length > 1 ? int.parse(parts[1]) : 0;
      final period = hour >= 12 ? 'PM' : 'AM';
      if (hour == 0) hour = 12;
      if (hour > 12) hour -= 12;
      return '$hour:${minutes.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time;
    }
  }

  String formatDateTime(String? dateTimeStr) {
    if (dateTimeStr == null) return 'Unknown date';

    try {
      final DateTime dateTime = DateTime.parse(dateTimeStr);
      final String month = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ][dateTime.month - 1];

      final String day = dateTime.day.toString();
      final String year = dateTime.year.toString();
      final String hour = dateTime.hour > 12
          ? (dateTime.hour - 12).toString()
          : dateTime.hour == 0
              ? '12'
              : dateTime.hour.toString();
      final String minute = dateTime.minute.toString().padLeft(2, '0');
      final String period = dateTime.hour >= 12 ? 'PM' : 'AM';

      return '$month $day, $year, $hour:$minute$period';
    } catch (e) {
      return dateTimeStr;
    }
  }

  Widget _buildExpandedOrderView(int orderId) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final itemSpacing = size.height * 0.02;
    final isLandscape = size.width > size.height;

    final cookingImageSize =
        isLandscape ? size.height * 0.30 : size.width * 0.40;
    final driverContainerWidth =
        isLandscape ? size.width * 0.2 : size.width * 0.45;
    final progressBarWidth =
        isLandscape ? size.width * 0.08 : size.width * 0.12;

    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is ViewOrederdetailsFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        } else if (state is ViewOrederdetailsSuccess) {
          final orderStatus = Initializer.viewOrderDetailsModel.data?.status;
          if (_mapController != null &&
              (orderStatus == 'DELIVERING' || orderStatus == 'PICKING_UP')) {
            setState(() {});
          }
        }
      },
      buildWhen: (previous, current) {
        return current is ViewOrederdetailsLoading ||
            current is ViewOrederdetailsSuccess ||
            current is GetCartCountSuccess ||
            current is ViewOrederdetailsFailed;
      },
      builder: (context, state) {
        if (state is ViewOrederdetailsLoading) {
          return const Center(child: CupertinoActivityIndicator());
        }

        if (state is ViewOrederdetailsFailed ||
            Initializer.viewOrderDetailsModel.data == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "Failed to Load Order Details",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  state is ViewOrederdetailsFailed
                      ? state.message
                      : "No order details available",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: const Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        final orderData = Initializer.viewOrderDetailsModel.data!;
        final chefName = orderData.chef?.name ?? 'Unknown Chef';
        final chefAvatar = orderData.chef?.photo != null
            ? ServerHelper.imageUrl + orderData.chef!.photo!
            : 'assets/images/chef_placeholder.png';
        final itemsCount = orderData.items?.length ?? 0;
        final deliveryAddress = [
          orderData.address?.addressText,
          orderData.address?.houseNumber,
          orderData.address?.buildingType,
          orderData.address?.landmark,
        ].where((element) => element != null && element.isNotEmpty).join(', ');

        final order_number = orderData.orderNumber ?? '1';
        final deliveryTimeDescription =
            orderData.deliveryTimes?.description ?? 'Unknown Time';
        final timeSlot = orderData.timeSlot;

        String formattedDeliveryTime = '';
        if (timeSlot != null &&
            timeSlot.startTime != null &&
            timeSlot.endTime != null) {
          final startTime = _formatTimeOnly(timeSlot.startTime!);
          final endTime = _formatTimeOnly(timeSlot.endTime!);
          formattedDeliveryTime = '$startTime - $endTime';
        } else {
          formattedDeliveryTime = deliveryTimeDescription;
        }
        final promotions = orderData.coupons
                ?.map((coupon) => coupon.couponCode ?? 'Unknown Promotion')
                .join(', ') ??
            'No Promotions';
        final subtotal = orderData.subtotal?.toStringAsFixed(2) ?? '0.00';
        final deliveryFee = orderData.deliveryFee?.toStringAsFixed(2) ?? '0.00';
        final serviceFee = orderData.serviceFee?.toStringAsFixed(2) ?? '0.00';
        final discount = orderData.discount?.toStringAsFixed(2) ?? '0.00';
        final walletCredits =
            orderData.walletCredits?.toStringAsFixed(2) ?? '0.00';
        final taxesAndFees =
            orderData.taxesAndFees?.toStringAsFixed(2) ?? '0.00';
        final total = orderData.total?.toStringAsFixed(2) ?? '0.00';
        final status = orderData.status ?? 'Unknown';

        if ((status == 'PICKING_UP' || status == 'DELIVERING') &&
            orderData.assignedDriver?.driverCurrentLocation?.location
                    ?.coordinates !=
                null &&
            orderData.address?.location?.coordinates != null) {
          final driverLocation = LatLng(
            orderData.assignedDriver!.driverCurrentLocation!.location!
                .coordinates![1],
            orderData.assignedDriver!.driverCurrentLocation!.location!
                .coordinates![0],
          );
          final customerLocation = LatLng(
            orderData.address!.location!.coordinates![1],
            orderData.address!.location!.coordinates![0],
          );

          bool shouldUpdateRoute = _lastDriverLocation == null ||
              _calculateDistance(_lastDriverLocation!, driverLocation) > 0.0001;

          if (shouldUpdateRoute) {
            _lastDriverLocation = driverLocation;
            _getRouteCoordinates(driverLocation, customerLocation)
                .then((points) {
              if (points.isNotEmpty) {
                setState(() {
                  _polylines = {
                    Polyline(
                      polylineId: const PolylineId('driver_to_customer_route'),
                      points: points,
                      color: const Color(0xFF007A4D),
                      width: 7,
                    ),
                  };
                });
              }
            });
          }
        } else {
          if (_polylines.isNotEmpty) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _polylines = {};
                _lastDriverLocation = null;
              });
            });
          }
        }

        return Container(
          color: const Color(0xFFF6F3EC),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: size.width * 0.04,
                  vertical: size.height * 0.02,
                ),
                child: GestureDetector(
                  onTap: _closeExpandedOrderView,
                  child: Row(
                    children: [
                      Icon(
                        Icons.chevron_left,
                        size: isLandscape ? size.height * 0.04 : twentyFour,
                        color: const Color(0xFF1F2122),
                      ),
                      const SizedBox(width: 8),
                      IntrinsicWidth(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              'Back',
                              style: TextStyle(
                                fontSize:
                                    isLandscape ? size.height * 0.03 : sixteen,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'Inter',
                                color: Color(0xFF1F2122),
                                decoration: TextDecoration.none,
                              ),
                            ),
                            SizedBox(height: screenWidth * 0.0025),
                            Container(
                              height: 1.5,
                              width: double.infinity,
                              color: Color(0xFF1F2122),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (status == 'DELIVERING')
                              Container(
                                constraints: BoxConstraints(
                                  maxWidth: double.infinity,
                                  maxHeight: 400,
                                ),
                                width: double.infinity,
                                height: 400,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: FutureBuilder<Set<Marker>>(
                                    future: _createMarkers(),
                                    builder: (context, markerSnapshot) {
                                      if (!markerSnapshot.hasData) {
                                        return const Center(
                                            child:
                                                CupertinoActivityIndicator());
                                      }
                                      return GoogleMap(
                                        key:
                                            ValueKey('map-${_selectedOrderId}'),
                                        initialCameraPosition: CameraPosition(
                                          target: LatLng(
                                            orderData
                                                    .assignedDriver
                                                    ?.driverCurrentLocation
                                                    ?.location
                                                    ?.coordinates?[1] ??
                                                orderData.chef?.location
                                                    ?.coordinates?[1] ??
                                                12.906241855246021,
                                            orderData
                                                    .assignedDriver
                                                    ?.driverCurrentLocation
                                                    ?.location
                                                    ?.coordinates?[0] ??
                                                orderData.chef?.location
                                                    ?.coordinates?[0] ??
                                                77.58580058813095,
                                          ),
                                          zoom: 13,
                                        ),
                                        gestureRecognizers: <Factory<
                                            OneSequenceGestureRecognizer>>[
                                          Factory<OneSequenceGestureRecognizer>(
                                              () => EagerGestureRecognizer()),
                                        ].toSet(),
                                        markers: markerSnapshot.data!,
                                        polylines: _polylines,
                                        zoomControlsEnabled: true,
                                        myLocationEnabled: true,
                                        myLocationButtonEnabled: true,
                                        zoomGesturesEnabled: true,
                                        scrollGesturesEnabled: true,
                                        onMapCreated:
                                            (GoogleMapController controller) {
                                          _mapController = controller;

                                          // Slight delay to ensure map is fully loaded before updating bounds
                                          // Only set the bounds once when the map is initially created
                                          if (!_initialMapBoundsSet) {
                                            Future.delayed(
                                                Duration(milliseconds: 300),
                                                () {
                                              if (mounted &&
                                                  _mapController != null) {
                                                _updateMapBounds();
                                              }
                                            });
                                          }
                                        },
                                      );
                                    },
                                  ),
                                ),
                              ),
                            SizedBox(height: itemSpacing),
                            Container(
                              padding: EdgeInsets.all(size.width * 0.05),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  if (status == 'PREPARING') ...[
                                    Image.asset(
                                      'assets/images/cooking.png',
                                      height: cookingImageSize,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Chef $chefName is preparing your order',
                                      style: TextStyle(
                                        fontSize: isLandscape
                                            ? size.height * 0.04
                                            : twenty,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black,
                                        fontFamily: 'Inter',
                                        height: 1.24,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Estimated arrival $formattedDeliveryTime',
                                      style: TextStyle(
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ] else if (status == 'CONFIRMED') ...[
                                    Image.asset(
                                      'assets/images/confirmed_order.png',
                                      height: cookingImageSize,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Order $order_number confirmed!',
                                      style: TextStyle(
                                        fontSize: isLandscape
                                            ? size.height * 0.04
                                            : twenty,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.black,
                                        fontFamily: 'Inter',
                                        height: 1.24,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'We have received your order and Chef $chefName is on it!',
                                      style: TextStyle(
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ] else if (status == 'ACTIVE') ...[
                                    Image.asset(
                                      'assets/images/confirmed_order.png',
                                      height: cookingImageSize,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Order $order_number placed!',
                                      style: TextStyle(
                                        fontSize: isLandscape
                                            ? size.height * 0.04
                                            : twenty,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.black,
                                        fontFamily: 'Inter',
                                        height: 1.24,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'We have received your order and Chef $chefName is on it!',
                                      style: TextStyle(
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ] else if (status == 'PICKING_UP') ...[
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Driver is picking up your order',
                                      style: TextStyle(
                                        fontSize: isLandscape
                                            ? size.height * 0.04
                                            : twenty,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black,
                                        fontFamily: 'Inter',
                                        height: 1.24,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Estimated delivery $formattedDeliveryTime',
                                      style: TextStyle(
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ] else if (status == 'DELIVERING') ...[
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Driver is now delivering your order',
                                      style: TextStyle(
                                        fontSize: isLandscape
                                            ? size.height * 0.04
                                            : twenty,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black,
                                        fontFamily: 'Inter',
                                        height: 1.24,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Estimated delivery $formattedDeliveryTime',
                                      style: TextStyle(
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Inter',
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ] else if (status == 'DELIVERED') ...[
                                    Image.asset(
                                      'assets/images/delivered.png',
                                      height: cookingImageSize,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Order successfully delivered!',
                                      style: TextStyle(
                                        fontSize: isLandscape
                                            ? size.height * 0.04
                                            : twenty,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.black,
                                        fontFamily: 'Inter',
                                        height: 1.24,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ] else if (status == 'CANCELLED') ...[
                                    Image.asset(
                                      'assets/icons/cancel.png',
                                      height: cookingImageSize * 0.6,
                                    ),
                                    SizedBox(height: itemSpacing * 0.5),
                                    Text(
                                      'Order $order_number has been cancelled',
                                      style: TextStyle(
                                        fontSize: isLandscape
                                            ? size.height * 0.04
                                            : twenty,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.black,
                                        fontFamily: 'Inter',
                                        height: 1.24,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                  SizedBox(height: itemSpacing),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: List.generate(5, (index) {
                                      int filledBars = 1;
                                      switch (status) {
                                        case 'ACTIVE':
                                        case 'CANCELLED':
                                          filledBars = 1;
                                          break;
                                        case 'PREPARING':
                                        case 'PICKING_UP':
                                          filledBars = 3;
                                          break;
                                        case 'DELIVERING':
                                          filledBars = 4;
                                          break;
                                        case 'DELIVERED':
                                          filledBars = 5;
                                          break;
                                        default:
                                          filledBars = 1;
                                      }
                                      return Container(
                                        width: progressBarWidth,
                                        height: size.height * 0.01,
                                        margin: EdgeInsets.symmetric(
                                            horizontal: size.width * 0.019),
                                        decoration: BoxDecoration(
                                          color: index < filledBars
                                              ? Colors.black
                                              : const Color(0xFFD9D9D9),
                                          borderRadius: BorderRadius.circular(
                                              size.width * 0.02),
                                        ),
                                      );
                                    }),
                                  ),
                                  SizedBox(height: itemSpacing),
                                  Text(
                                    'Your order will be delivered to $deliveryAddress at $formattedDeliveryTime',
                                    style: TextStyle(
                                      fontSize: twelve,
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Inter',
                                      color: const Color(0xFF1F2122),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(height: itemSpacing),
                                  if ((status == 'DELIVERING' ||
                                          status == 'PICKING_UP') &&
                                      orderData.assignedDriver != null &&
                                      (orderData.assignedDriver?.firstName
                                              ?.isNotEmpty ??
                                          false ||
                                              (orderData.assignedDriver
                                                          ?.lastName !=
                                                      null &&
                                                  orderData.assignedDriver!
                                                      .lastName!.isNotEmpty)))
                                    Container(
                                      width: driverContainerWidth,
                                      padding:
                                          EdgeInsets.all(size.width * 0.02),
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFF1F2F3),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Column(
                                        children: [
                                          Text(
                                            'Your Driver',
                                            style: TextStyle(
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              fontFamily: 'Inter',
                                              color: const Color(0xFF414346),
                                            ),
                                          ),
                                          SizedBox(height: itemSpacing * 0.2),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              CircleAvatar(
                                                radius: isLandscape
                                                    ? size.height * 0.025
                                                    : baseTextSize,
                                                child: ClipOval(
                                                  child: CachedNetworkImage(
                                                    imageUrl: orderData
                                                            .assignedDriver
                                                            ?.profilePictureUrl ??
                                                        '',
                                                    placeholder:
                                                        (context, url) =>
                                                            Container(
                                                      color: Colors.grey[300],
                                                      child: const Center(
                                                        child:
                                                            CircularProgressIndicator(
                                                          strokeWidth: 2,
                                                        ),
                                                      ),
                                                    ),
                                                    errorWidget:
                                                        (context, url, error) =>
                                                            Image.asset(
                                                      'assets/images/driver.png',
                                                      fit: BoxFit.cover,
                                                    ),
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                  width: size.width * 0.01),
                                              Text(
                                                '${orderData.assignedDriver?.firstName ?? ''} ${orderData.assignedDriver?.lastName ?? ''}',
                                                style: TextStyle(
                                                  fontSize: forteen,
                                                  fontWeight: FontWeight.w600,
                                                  fontFamily: 'Inter',
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: itemSpacing * 0.2),
                                          Text(
                                            '${orderData.assignedDriver?.phone ?? 'Unknown'}',
                                            style: TextStyle(
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              fontFamily: 'Inter',
                                              color: const Color(0xFF414346),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  SizedBox(height: forteen),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'Need help? ',
                                        style: TextStyle(
                                          fontSize: twelve,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  CustomerSupport(
                                                orderId: Initializer
                                                        .viewOrderDetailsModel
                                                        .data
                                                        ?.id ??
                                                    0,
                                              ),
                                            ),
                                          );
                                        },
                                        child: Text(
                                          'Contact support.',
                                          style: TextStyle(
                                            fontSize: twelve,
                                            fontFamily: 'Inter',
                                            fontWeight: FontWeight.w700,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Order Summary card
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(size.width * 0.05),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Order Summary',
                                style: TextStyle(
                                  fontSize: isLandscape
                                      ? size.height * 0.04
                                      : eighteen,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF000000),
                                ),
                              ),
                              DottedDivider(),
                              SizedBox(height: itemSpacing * 0.3),
                              Text(
                                'Ordered From',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Row(
                                children: [
                                  CircleAvatar(
                                    radius: isLandscape
                                        ? size.height * 0.015
                                        : twelve,
                                    backgroundImage: NetworkImage(chefAvatar),
                                  ),
                                  SizedBox(width: size.width * 0.02),
                                  Text(
                                    chefName,
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              DottedDivider(),
                              SizedBox(height: itemSpacing * 0.3),
                              Text(
                                'Order Details ($itemsCount Items)',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.8),
                              if (orderData.items != null)
                                ...orderData.items!.map((item) {
                                  return Column(
                                    children: [
                                      _buildOrderItem(
                                        '${item.quantity}x',
                                        item.dish?.name ?? 'Unknown Dish',
                                        '\$${item.totalPrice?.toStringAsFixed(2) ?? '0.00'}',
                                      ),
                                      SizedBox(height: itemSpacing * 0.5),
                                    ],
                                  );
                                }),
                              DottedDivider(),
                              SizedBox(height: itemSpacing * 0.3),
                              Text(
                                'Promotions',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Text(
                                (promotions == null ||
                                        promotions.trim().isEmpty)
                                    ? '- - -'
                                    : promotions,
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              DottedDivider(),
                              SizedBox(height: itemSpacing * 0.3),
                              Text(
                                'Order Total',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.8),
                              Column(
                                children: [
                                  _buildTotalItem('Subtotal', '\$$subtotal'),
                                  _buildTotalItem(
                                      'Delivery fee', '\$$deliveryFee'),
                                  _buildTotalItem(
                                      'Service fee', '\$$serviceFee'),
                                  _buildTotalItem('Discounts', '-\$$discount'),
                                  _buildTotalItem('Eatro Wallet Credits',
                                      '-\$$walletCredits'),
                                  _buildTotalItem(
                                      'Sales Tax', '\$$taxesAndFees'),
                                  DottedDivider(),
                                  _buildTotalItem('Total', '\$$total',
                                      isTotal: true),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Delivery Details card
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(size.width * 0.05),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Delivery Details',
                                style: TextStyle(
                                  fontSize: isLandscape
                                      ? size.height * 0.04
                                      : eighteen,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF000000),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              Text(
                                'Address',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(top: 2),
                                    child: Icon(
                                      Icons.location_on_outlined,
                                      size: baseTextSize * 1.2,
                                      color: const Color(0xFF414346),
                                    ),
                                  ),
                                  SizedBox(width: size.width * 0.01),
                                  SizedBox(
                                    width: size.width * 0.7,
                                    child: Text(
                                      deliveryAddress.isNotEmpty
                                          ? deliveryAddress
                                          : 'No address',
                                      style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w500,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing),
                              Text(
                                'Delivery',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Inter-medium',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Row(
                                children: [
                                  Icon(Icons.access_time,
                                      size: baseTextSize * 1.0,
                                      color: const Color(0xFF414346)),
                                  SizedBox(width: size.width * 0.02),
                                  Text(
                                    formattedDeliveryTime,
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing),
                              Divider(
                                height: 0,
                                thickness: 1,
                                color: const Color(0xFFE1E3E6),
                              ),
                              SizedBox(height: itemSpacing),
                              Text(
                                'Drop-Off Options',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontFamily: 'Inter-medium',
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Text(
                                orderData.dropOffOption?.name ?? 'Unknown',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Inter',
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              Text(
                                'Drop-Off Instructions',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontFamily: 'Inter-medium',
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                              Text(
                                orderData.dropOffInstructions ?? 'None',
                                style: TextStyle(
                                  fontSize: forteen,
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xFF1F2122),
                                ),
                              ),
                              SizedBox(height: itemSpacing),
                              Divider(
                                height: 0,
                                thickness: 1,
                                color: const Color(0xFFE1E3E6),
                              ),
                              SizedBox(height: itemSpacing),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Delivery Time',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Inter-medium',
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Text(
                                    (orderData.deliveryTimes?.description ??
                                            '_')
                                        .replaceFirst('Delivery between ', ''),
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Image.asset(
                                    'assets/icons/star_2.png',
                                    width: baseTextSize * 1.0,
                                    height: baseTextSize * 1.0,
                                    color: const Color(0xFF414346),
                                  ),
                                  SizedBox(width: size.width * 0.02),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          orderData.deliveryTimes?.name ??
                                              'Priority',
                                          style: TextStyle(
                                            fontSize: forteen,
                                            fontWeight: FontWeight.w500,
                                            color: const Color(0xFF1F2122),
                                          ),
                                        ),
                                        SizedBox(height: itemSpacing * 0.2),
                                        Text(
                                          (orderData.deliveryTimes
                                                      ?.description ??
                                                  '30-60 Minutes')
                                              .replaceFirst(
                                                  'Delivery between ', ''),
                                          style: TextStyle(
                                            fontSize: twelve,
                                            fontFamily: 'Inter',
                                            fontWeight: FontWeight.w400,
                                            color: const Color(0xFF414346),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Text(
                                    '+\$${orderData.deliveryTimes?.cost ?? '0.00'}',
                                    style: TextStyle(
                                      fontSize: forteen,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: itemSpacing * 0.5),
                            ],
                          ),
                        ),
                      ),
                      // Payment card
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(size.width * 0.04),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Payment',
                                    style: TextStyle(
                                      fontSize: forteen * 1.0,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xFF1F2122),
                                    ),
                                  ),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFE8F5E9),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Image.asset(
                                          'assets/icons/tick.png',
                                          width: 12,
                                          height: 12,
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          'Paid',
                                          style: TextStyle(
                                            fontSize: twelve,
                                            color: const Color(0xFF2E7D32),
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: size.width * 0.03),
                              Builder(
                                builder: (_) {
                                  final walletCreditsVal =
                                      orderData.walletCredits ?? 0.0;
                                  final totalVal = orderData.total ?? 0.0;
                                  if (walletCreditsVal > 0 && totalVal > 0) {
                                    return Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.attach_money,
                                              size: baseTextSize * 1.2,
                                              color: const Color(0xFF1F2122),
                                            ),
                                            SizedBox(width: size.width * 0.03),
                                            Expanded(
                                              child: Text(
                                                'Paid through Online',
                                                style: TextStyle(
                                                  fontSize: forteen,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: size.width * 0.01),
                                        Padding(
                                          padding: EdgeInsets.only(
                                              left: size.width * 0.02),
                                          child: Text(
                                            '      Expires 03/2028',
                                            style: TextStyle(
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ),
                                        SizedBox(height: size.width * 0.02),
                                        Row(
                                          children: [
                                            Image.asset(
                                              'assets/icons/db_wallet.png',
                                              height: baseTextSize * 1.2,
                                              fit: BoxFit.contain,
                                            ),
                                            SizedBox(width: size.width * 0.03),
                                            Expanded(
                                              child: Text(
                                                'Paid partially with Eatro Wallet',
                                                style: TextStyle(
                                                  fontSize: forteen,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      const Color(0xFF1F2122),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    );
                                  } else if (totalVal == 0 &&
                                      walletCreditsVal > 0) {
                                    return Row(
                                      children: [
                                        Image.asset(
                                          'assets/icons/payment.png',
                                          height: baseTextSize * 0.8,
                                          fit: BoxFit.contain,
                                        ),
                                        SizedBox(width: size.width * 0.03),
                                        Expanded(
                                          child: Text(
                                            'Paid through Eatro Wallet',
                                            style: TextStyle(
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  } else if (walletCreditsVal == 0 &&
                                      totalVal > 0) {
                                    return Row(
                                      children: [
                                        Icon(
                                          Icons.attach_money,
                                          size: baseTextSize * 1.2,
                                          color: const Color(0xFF1F2122),
                                        ),
                                        SizedBox(width: size.width * 0.03),
                                        Expanded(
                                          child: Text(
                                            'Paid through Online',
                                            style: TextStyle(
                                              fontSize: forteen,
                                              fontWeight: FontWeight.w400,
                                              color: const Color(0xFF1F2122),
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  } else {
                                    return Text(
                                      'No payment information available',
                                      style: TextStyle(
                                        fontSize: forteen,
                                        fontWeight: FontWeight.w400,
                                        color: const Color(0xFF1F2122),
                                      ),
                                    );
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Email notification
                      Padding(
                        padding: EdgeInsets.all(size.width * 0.04),
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(size.width * 0.02),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE1DDD5),
                            borderRadius: BorderRadius.circular(6),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Align(
                                alignment: Alignment.topCenter,
                                child: Icon(
                                  Icons.mail_outline,
                                  size: baseTextSize * 1.25,
                                  color: const Color(0xFF414346),
                                ),
                              ),
                              SizedBox(width: size.width * 0.02),
                              Expanded(
                                child: Text(
                                  'A copy of your invoice is on its way to your inbox. Please check your email.',
                                  style: TextStyle(
                                    fontSize: forteen,
                                    fontFamily: 'Inter',
                                    color: const Color(0xFF1F2122),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: screenHeight * 0.08),
                    ],
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildOrderItem(String quantity, String title, String price) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final isLandscape = size.width > size.height;

    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: size.width * 0.02,
            vertical: size.height * 0.005,
          ),
          decoration: BoxDecoration(
            color: Color(0xFFE1E3E6),
            borderRadius: BorderRadius.circular(size.width * 0.04),
          ),
          child: Text(
            quantity,
            style: TextStyle(
              fontSize: isLandscape ? size.height * 0.022 : forteen,
              // fontWeight: FontWeight.w500,
              fontFamily: 'Inter-medium',
              color: Color(0xFF1F2122),
            ),
          ),
        ),
        SizedBox(width: size.width * 0.03),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: isLandscape ? size.height * 0.025 : forteen,
              // fontWeight: FontWeight.w600,
              fontFamily: 'Inter-Semibold',
              color: Color(0xFF1F2122),
            ),
          ),
        ),
        Text(
          price,
          style: TextStyle(
            fontSize: isLandscape ? size.height * 0.022 : forteen,
            fontWeight: FontWeight.w400,
            fontFamily: 'Inter',
            color: Color(0xFF414346),
          ),
        ),
      ],
    );
  }

  Widget _buildTotalItem(String label, String amount, {bool isTotal = false}) {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final isLandscape = size.width > size.height;
    final isDiscount = label == 'Discounts' || label == 'DB Wallet Credits';

    return Padding(
      padding: EdgeInsets.only(
        bottom: isTotal ? 0 : size.height * 0.01,
        left: size.width * 0.01,
        right: size.width * 0.01,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal
                  ? (isLandscape ? size.height * 0.03 : sixteen)
                  : (isLandscape ? size.height * 0.025 : forteen),
              fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
              color: Color(0xFF1F2122),
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: isTotal
                  ? (isLandscape ? size.height * 0.03 : sixteen)
                  : (isLandscape ? size.height * 0.022 : forteen),
              fontFamily: isTotal ? 'Inter-Semibold' : 'Inter',
              color: isDiscount ? Color(0xFFD31510) : Color(0xFF414346),
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOngoingOrders() {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final itemSpacing = size.height * 0.015;

    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is BookingListFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      buildWhen: (previous, current) {
        return current is BookingListLoading ||
            current is BookingListSuccess ||
            current is BookingListFailed;
      },
      builder: (context, state) {
        if (state is BookingListLoading && _ongoingCurrentPage == 1) {
          return const Center(child: CupertinoActivityIndicator());
        }

        if (state is BookingListSuccess) {
          final newBookings = state.bookings.cast<Bookings>();
          final existingIds = _ongoingBookings.map((b) => b.id).toSet();
          final uniqueNewBookings =
              newBookings.where((b) => !existingIds.contains(b.id)).toList();

          _ongoingBookings = List<Bookings>.from(_ongoingBookings)
            ..addAll(uniqueNewBookings);
          _ongoingTotalPages =
              Initializer.ongoingBookinglistmodel.data?.pagination?.pages ?? 1;
          _isLoadingMoreOngoing = false;
        }

        if (Initializer.ongoingBookinglistmodel.data == null ||
            Initializer.ongoingBookinglistmodel.data!.bookings!.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "No Ongoing Orders Right Now!",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  "Looks like you haven’t placed any orders yet.\nStart exploring delicious meals!",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          controller: _ongoingScrollController,
          padding: EdgeInsets.only(
            left: size.width * 0.05,
            right: size.width * 0.05,
            bottom: screenHeight * 0.08,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Column(
              children: [
                ...List.generate(
                  _ongoingBookings.length,
                  (index) {
                    final booking = _ongoingBookings[index];
                    return GestureDetector(
                      onTap: () => _showExpandedOrder(booking.id!),
                      child: Padding(
                        padding: EdgeInsets.fromLTRB(
                          size.width * 0.04,
                          index == 0 ? size.width * 0.075 : size.width * 0.04,
                          size.width * 0.04,
                          index < _ongoingBookings.length - 1
                              ? size.width * 0.002
                              : size.width * 0.025,
                        ),
                        child: Column(
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Avatar
                                GestureDetector(
                                  onTap: () => _showExpandedOrder(booking.id!),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(
                                        size.width * 0.06),
                                    child: Image.network(
                                      ServerHelper.imageUrl +
                                          booking.chef!.photo.toString(),
                                      width: size.width * 0.11,
                                      height: size.width * 0.11,
                                      fit: BoxFit.cover,
                                      errorBuilder: (_, __, ___) => Icon(
                                          Icons.person,
                                          size: size.width * 0.06),
                                    ),
                                  ),
                                ),
                                SizedBox(width: size.width * 0.03),
                                // Info Block
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      GestureDetector(
                                        onTap: () =>
                                            _showExpandedOrder(booking.id!),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Text(
                                                booking.chef!.name
                                                    .toString()
                                                    .split(' ')
                                                    .map((word) => word
                                                            .isNotEmpty
                                                        ? word[0]
                                                                .toUpperCase() +
                                                            (word.length > 1
                                                                ? word
                                                                    .substring(
                                                                        1)
                                                                    .toLowerCase()
                                                                : '')
                                                        : '')
                                                    .join(' '),
                                                style: TextStyle(
                                                  fontSize: baseTextSize * 1.1,
                                                  fontWeight: FontWeight.w600,
                                                  fontFamily: 'Inter-Semibold',
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            GestureDetector(
                                              onTap: () {
                                                setState(() {
                                                  _selectedOrderId = booking.id;
                                                  _selectedOrder = {
                                                    'chefName':
                                                        booking.chef!.name,
                                                    'avatar':
                                                        ServerHelper.imageUrl +
                                                            booking.chef!.photo
                                                                .toString(),
                                                    'id': _selectedOrderId,
                                                  };
                                                });
                                              },
                                              child: Row(
                                                children: [
                                                  GestureDetector(
                                                    onTap: () =>
                                                        _showExpandedOrder(
                                                            booking.id!),
                                                    child: IntrinsicWidth(
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          Text(
                                                            '${booking.itemsCount ?? 0} items',
                                                            style: TextStyle(
                                                              fontSize:
                                                                  baseTextSize *
                                                                      0.8,
                                                              fontFamily:
                                                                  'Inter-Semibold',
                                                            ),
                                                          ),
                                                          SizedBox(height: 1),
                                                          Container(
                                                            height: 1,
                                                            width:
                                                                double.infinity,
                                                            color: Colors.black,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 4),
                                                  GestureDetector(
                                                    onTap: () =>
                                                        _showExpandedOrder(
                                                            booking.id!),
                                                    child: Image.asset(
                                                      'assets/icons/right_arrow_black.png',
                                                      width: baseTextSize,
                                                      height: baseTextSize,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(height: itemSpacing * 0.4),
                                      // Time row
                                      Row(
                                        children: [
                                          Image.asset(
                                            'assets/icons/calender_2.png',
                                            width: 13,
                                            height: 13,
                                            color: const Color(0xFF414346),
                                          ),
                                          SizedBox(width: 4),
                                          Text(
                                            'Open ${booking.chef!.chefOperationTime != null ? '${to12HourFormat(booking.chef!.chefOperationTime!.startTime ?? "")} - ${to12HourFormat(booking.chef!.chefOperationTime!.endTime ?? "")}' : ""}',
                                            style: TextStyle(
                                              fontSize: baseTextSize * 0.7,
                                              fontFamily: 'Inter',
                                              color: const Color(0xFF414346),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: itemSpacing * 0.3),
                                      // Operation days + status
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              (booking.chef!.chefOperationDays
                                                          as List<dynamic>?)
                                                      ?.map((day) => day
                                                              .toString()
                                                              .isNotEmpty
                                                          ? day.toString()[0]
                                                          : '')
                                                      .join(', ') ??
                                                  '',
                                              style: TextStyle(
                                                fontSize: baseTextSize * 0.7,
                                                fontFamily: 'Inter',
                                                color: const Color(0xFF414346),
                                              ),
                                            ),
                                          ),
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                              horizontal: size.width * 0.020,
                                              vertical: size.height * 0.001,
                                            ),
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFE1E3E6),
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                            ),
                                            child: Text(
                                              booking.status.toString(),
                                              style: TextStyle(
                                                fontSize: baseTextSize * 0.7,
                                                fontFamily: 'Inter-medium',
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            if (index < _ongoingBookings.length - 1)
                              Padding(
                                padding:
                                    EdgeInsets.only(top: itemSpacing * 1.5),
                                child: Divider(
                                  height: 0,
                                  thickness: 1,
                                  color: const Color(0xFFE1E3E6),
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
                // Pagination loader
                if (_isLoadingMoreOngoing)
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: CupertinoActivityIndicator(),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatDateRange(String? startDate, String? endDate) {
    if (startDate == null || endDate == null) return 'Unknown Date Range';

    try {
      final DateTime start = DateTime.parse(startDate);
      final DateTime end = DateTime.parse(endDate);
      final DateFormat formatter = DateFormat('MMMM d');
      final String startFormatted = formatter.format(start);
      final String endFormatted = formatter.format(end);
      return '$startFormatted to $endFormatted';
    } catch (e) {
      return '$startDate to $endDate';
    }
  }

  String _formatStatusText(String status) {
    if (status.isEmpty) return status;
    return status[0].toUpperCase() + status.substring(1).toLowerCase();
  }

  Widget _buildMealPlans() {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final contentPadding = getResponsivePadding(context);
    final itemSpacing = size.height * 0.02;
    final isLandscape = size.width > size.height;

    final cardPadding = EdgeInsets.symmetric(
      horizontal: size.width * 0.01,
      vertical: size.width * 0.03,
    );

    final elementSpacing = size.height * 0.01;

    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {},
      buildWhen: (previous, current) {
        return current is MealPlanListLoading ||
            current is MealPlanListSuccess ||
            current is MealPlanListFailed;
      },
      builder: (context, state) {
        if (state is MealPlanListLoading) {
          return const Center(child: CupertinoActivityIndicator());
        }

        if (state is MealPlanListFailed) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "Failed to Load Meal Plans",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  state.message,
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: const Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        if (Initializer.mealPlanListModel.data == null ||
            Initializer.mealPlanListModel.data!.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "No Meal Plans Available",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  "Looks like you haven't subscribed to any meal plans yet.\nStart exploring now!",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: const Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: EdgeInsets.only(
            left: size.width * 0.05,
            right: size.width * 0.05,
            bottom: screenHeight * 0.08,
          ),
          child: Container(
            padding: EdgeInsets.only(left: 16, right: 0),
            margin: EdgeInsets.only(bottom: size.height * 0.02),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: List.generate(
                Initializer.mealPlanListModel.data!.length,
                (index) {
                  final plan = Initializer.mealPlanListModel.data![index];
                  final String title =
                      _formatDateRange(plan.startDate, plan.endDate);
                  final String deliveryProgress =
                      '${plan.deliveredDays ?? 0} / ${plan.totalDays ?? 0} Delivered';
                  final String dishesPerDay =
                      '${plan.dishesPerDay ?? 0} ${(plan.dishesPerDay ?? 0) == 1 ? 'meal' : 'meals'} a day';

                  return GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => WithNavBar(
                            currentIndex: 1,
                            child: OrdersPage(id: plan.id ?? 0),
                          ),
                        ),
                      );
                    },
                    child: Column(
                      children: [
                        Container(
                          padding: cardPadding,
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: FittedBox(
                                      fit: BoxFit.scaleDown,
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        title,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: sixteen,
                                          fontFamily: 'Inter',
                                          color: const Color(0xFF1F2122),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: size.width * 0.02),

                                  Container(
                                    alignment: Alignment.centerRight,
                                    padding: EdgeInsets.symmetric(
                                      horizontal: size.width * 0.02,
                                      vertical: size.height * 0.001,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          _getStatusColor(plan.status ?? '')[0],
                                      borderRadius: BorderRadius.circular(
                                          size.width * 0.04),
                                    ),
                                    child: Text(
                                      _formatStatusText(
                                          plan.status ?? 'Unknown'),
                                      style: TextStyle(
                                        color: _getStatusColor(
                                            plan.status ?? '')[1],
                                        fontSize: twelve,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Inter',
                                      ),
                                    ),
                                  ),

                                  // More options icon
                                  //   IconButton(  padding: EdgeInsets.zero,
                                  //  //   padding: EdgeInsets.all(size.width * 0.01),
                                  //     constraints: BoxConstraints(
                                  //       minWidth: forteen,
                                  //       minHeight: forteen,
                                  //     ),
                                  //     icon: Icon(
                                  //       Icons.more_vert,
                                  //       size: sixteen,
                                  //       color: const Color(0xFF1F2122),
                                  //     ),
                                  //     onPressed: () {
                                  //       // Handle more options
                                  //     },
                                  //   ),

                                  SizedBox(
                                    width: twenty + twenty,
                                    height: eighteen,
                                    child: IconButton(
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                      icon: Icon(
                                        Icons.more_vert,
                                        size: sixteen,
                                        color: const Color(0xFF1F2122),
                                      ),
                                      onPressed: () {},
                                    ),
                                  )
                                ],
                              ),
                              SizedBox(
                                height: sixteen / 2,
                              ),
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // SizedBox(height: elementSpacing * 0.8),
                                    Text(
                                      dishesPerDay,
                                      style: TextStyle(
                                        color: const Color(0xFF414346),
                                        fontSize: forteen,
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    SizedBox(height: elementSpacing * 0.8),
                                    Text(
                                      deliveryProgress,
                                      style: TextStyle(
                                        color: const Color(0xFF1F2122),
                                        fontSize: forteen,
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (index <
                            Initializer.mealPlanListModel.data!.length - 1)
                          Divider(
                            height: 1,
                            thickness: 1,
                            color: const Color(0xFFE1E3E6),
                            indent: size.width * 0.04,
                            endIndent: size.width * 0.04,
                          ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPastOrders() {
    final size = MediaQuery.of(context).size;
    final baseTextSize = getResponsiveSize(context);
    final itemSpacing = size.height * 0.015;

    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is BookingListFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        } else if (state is ReorderSuccess) {
          final chefId = _getChefIdFromLastReorder();
          if (chefId != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CartPage2(chef_id: chefId),
              ),
            );
          }
        } else if (state is ReorderFailed) {
          _showReorderErrorDialog(state.message);
        }
      },
      buildWhen: (previous, current) =>
          current is BookingListLoading ||
          current is BookingListSuccess ||
          current is BookingListFailed ||
          current is ReorderLoading ||
          current is ReorderSuccess ||
          current is ReorderFailed,
      builder: (context, state) {
        if (state is BookingListLoading && _pastCurrentPage == 1) {
          return const Center(child: CupertinoActivityIndicator());
        }

        if (state is ReorderLoading) {
          return const Center(child: CupertinoActivityIndicator());
        }

        if (state is BookingListSuccess) {
          final newBookings = state.bookings.cast<Bookings>();
          final existingIds = _pastBookings.map((b) => b.id).toSet();
          final uniqueNewBookings =
              newBookings.where((b) => !existingIds.contains(b.id)).toList();

          _pastBookings = List<Bookings>.from(_pastBookings)
            ..addAll(uniqueNewBookings);
          _pastTotalPages =
              Initializer.ongoingBookinglistmodel.data?.pagination?.pages ?? 1;
          _isLoadingMorePast = false;
        }

        if (_pastBookings.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: size.width * 0.25,
                  child: Lottie.asset(
                    'assets/noorderes.json',
                    fit: BoxFit.contain,
                  ),
                ),
                Text(
                  "No Past Orders",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 16, medium: 18, large: 22, xlarge: 26),
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2122),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: size.height * 0.01),
                Text(
                  "You haven't placed any orders yet",
                  style: TextStyle(
                    fontSize: getResponsiveSize(context,
                        small: 12, medium: 14, large: 16, xlarge: 18),
                    color: const Color(0xFF66696D),
                    fontFamily: 'Inter',
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          controller: _pastScrollController,
          padding: EdgeInsets.only(
            left: size.width * 0.05,
            right: size.width * 0.05,
            bottom: screenHeight * 0.08,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                ...List.generate(_pastBookings.length, (index) {
                  final booking = _pastBookings[index];
                  return Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: size.width * 0.04,
                          vertical: index == 0
                              ? size.height * 0.019
                              : size.height * 0.03,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                ClipRRect(
                                  borderRadius:
                                      BorderRadius.circular(size.width * 0.06),
                                  child: Image.network(
                                    ServerHelper.imageUrl +
                                        (booking.chef?.photo ?? ''),
                                    width: 40,
                                    height: 40,
                                    fit: BoxFit.cover,
                                    errorBuilder: (_, __, ___) => Icon(
                                      Icons.person,
                                      size: size.width * 0.06,
                                    ),
                                  ),
                                ),
                                SizedBox(width: size.width * 0.03),
                                Expanded(
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Flexible(
                                        child: Text(
                                          booking.chef?.name ?? 'Unknown Chef',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontSize: 16,
                                            fontFamily: 'Inter',
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      Text(
                                        '\$${booking.total?.toStringAsFixed(2) ?? '0.00'}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: itemSpacing * 0.6),
                            Padding(
                              padding: EdgeInsets.only(left: size.width * 0.01),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${booking.itemsCount ?? 0} items',
                                    style: TextStyle(
                                      color: const Color(0xFF414346),
                                      fontSize: 14,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                  SizedBox(height: size.height * 0.007),
                                  Text(
                                    formatDateTime(booking.createdAt),
                                    style: TextStyle(
                                      color: const Color(0xFF414346),
                                      fontSize: 14,
                                      fontFamily: 'Inter',
                                    ),
                                  ),
                                  SizedBox(height: itemSpacing * 0.8),
                                  if (booking.status == 'DELIVERED')
                                    Wrap(
                                      spacing: size.width * 0.03,
                                      runSpacing: size.height * 0.01,
                                      children: [
                                        _buildActionButton(
                                          'Rate Driver',
                                          () => Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  DriverRatingPage(
                                                orderId: booking.id ?? 0,
                                              ),
                                            ),
                                          ),
                                          baseTextSize,
                                        ),
                                        _buildActionButton(
                                          'Leave chef a review',
                                          () => Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  ChefReviewPage(
                                                chefName:
                                                    booking.chef?.name ?? '',
                                                chefImage:
                                                    booking.chef?.photo ?? '',
                                                orderId: booking.id ?? 0,
                                                chefId: booking.chef?.id ?? 0,
                                              ),
                                            ),
                                          ),
                                          baseTextSize,
                                        ),
                                        _buildActionButton(
                                          'Reorder',
                                          () {
                                            _lastReorderChefId =
                                                booking.chef?.id;
                                            context.read<OrderBloc>().add(
                                                  ReorderEvent({
                                                    'booking_id': booking.id,
                                                  }),
                                                );
                                          },
                                          baseTextSize,
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (index < _pastBookings.length - 1)
                        const Divider(
                          height: 1,
                          thickness: 1,
                          color: Color(0xFFE1E3E6),
                        ),
                    ],
                  );
                }),
                if (_isLoadingMorePast)
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: CupertinoActivityIndicator(),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton(
      String text, VoidCallback onTap, double baseTextSize) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IntrinsicWidth(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IntrinsicWidth(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Color(0xFF414346),
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Text(
                      text,
                      style: TextStyle(
                        color: Color(0xFF414346),
                        fontSize: 10,
                        fontWeight: FontWeight.w400,
                        decoration: TextDecoration.none,
                      ),
                    ),
                  ),
                )
                // SizedBox(height: 1), // spacing between text and line
                // Container(
                //   height: 1, // thickness of underline
                //   width: double.infinity, // expands to match text width
                //   color: Color(0xFF414346),
                // ),
              ],
            ),
          ),
          SizedBox(width: 4),
          Image.asset(
            'assets/icons/black_right_arrow.png',
            width: baseTextSize,
            height: baseTextSize,
          ),
        ],
      ),
    );
  }

  List<Color> _getStatusColor(String status) {
    switch (status) {
      case 'Action Required':
        return [const Color(0xFFFFEBE7), const Color(0xFFD31510)];
      case 'ONGOING':
        return [const Color(0xFFCEF8E0), const Color(0xFF007A4D)];
      case 'SCHEDULED':
        return [const Color(0xFFE1E3E6), const Color(0xFF1F2122)];
      default:
        return [Colors.grey[200]!, Colors.black87];
    }
  }

  String to12HourFormat(String? time) {
    if (time == null || time.isEmpty) return "";
    try {
      final parts = time.split(':');
      int hour = int.parse(parts[0]);
      // We don't need minutes for this format
      String period = hour >= 12 ? 'PM' : 'AM';

      if (hour == 0) {
        hour = 12;
      } else if (hour > 12) {
        hour -= 12;
      }

      final hourStr = hour < 10 ? '0$hour' : '$hour';
      return '$hourStr $period';
    } catch (e) {
      return time;
    }
  }

  void _showExpandedOrder(int orderId) async {
    _locationRefreshTimer?.cancel();

    context.read<OrderBloc>().add(ViewOrederdetailsEvent(orderId));

    setState(() {
      _selectedOrderId = orderId;
      _selectedOrder = {};
      _polylines = {};
      _expandedOrderViewCache = null;
      _expandedOrderViewCacheId = orderId;
      _initialMapBoundsSet = false;
    });

    _startLocationRefreshTimer();
  }

  final Map<String, BitmapDescriptor> _iconCache = {};

  double _calculateBearing(LatLng start, LatLng end) {
    final double lat1 = start.latitude * (pi / 180);
    final double lat2 = end.latitude * (pi / 180);
    final double deltaLng = (end.longitude - start.longitude) * (pi / 180);

    final double y = sin(deltaLng) * cos(lat2);
    final double x =
        cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(deltaLng);

    final double bearing = atan2(y, x) * (180 / pi);
    return (bearing + 360) % 360;
  }

  LatLng? _getNextRoutePoint(LatLng currentLocation) {
    if (_polylines.isEmpty) return null;

    final polyline = _polylines.first;
    final points = polyline.points;

    if (points.length < 2) return null;

    double minDistance = double.infinity;
    int closestIndex = 0;

    for (int i = 0; i < points.length; i++) {
      final distance = _calculateDistance(currentLocation, points[i]);
      if (distance < minDistance) {
        minDistance = distance;
        closestIndex = i;
      }
    }

    final nextIndex = min(closestIndex + 3, points.length - 1);
    return nextIndex > closestIndex ? points[nextIndex] : null;
  }

  Future<Set<Marker>> _createMarkers() async {
    final orderData = Initializer.viewOrderDetailsModel.data!;
    final Set<Marker> markers = {};

    try {
      if (!_iconCache.containsKey('chef')) {
        _iconCache['chef'] =
            await getResizedMarker('assets/icons/chef_icon.png', 40);
      }
      final chefIcon = _iconCache['chef']!;

      if (!_iconCache.containsKey('driver')) {
        _iconCache['driver'] =
            await getResizedMarker('assets/icons/bike_icon.png', 40);
      }
      final driverIcon = _iconCache['driver']!;

      if (!_iconCache.containsKey('customer')) {
        _iconCache['customer'] =
            await getResizedMarker('assets/icons/customer_icon.png', 40);
      }
      final customerIcon = _iconCache['customer']!;

      if (orderData.chef?.location?.coordinates != null &&
          orderData.chef!.location!.coordinates!.length >= 2) {
        markers.add(
          Marker(
            markerId: const MarkerId('chef'),
            position: LatLng(
              orderData.chef!.location!.coordinates![1],
              orderData.chef!.location!.coordinates![0],
            ),
            icon: chefIcon,
            infoWindow:
                InfoWindow(title: 'Chef: ${orderData.chef?.name ?? "Chef"}'),
          ),
        );
      }

      if (orderData.assignedDriver != null &&
          orderData.assignedDriver!.driverCurrentLocation != null &&
          orderData.assignedDriver!.driverCurrentLocation!.location != null &&
          orderData.assignedDriver!.driverCurrentLocation!.location!
                  .coordinates !=
              null &&
          orderData.assignedDriver!.driverCurrentLocation!.location!
                  .coordinates!.length >=
              2) {
        final driverPosition = LatLng(
          orderData
              .assignedDriver!.driverCurrentLocation!.location!.coordinates![1],
          orderData
              .assignedDriver!.driverCurrentLocation!.location!.coordinates![0],
        );

        double rotation = 0.0;
        final nextPoint = _getNextRoutePoint(driverPosition);
        if (nextPoint != null) {
          rotation = _calculateBearing(driverPosition, nextPoint);
        }

        markers.add(
          Marker(
            markerId: const MarkerId('driver'),
            position: driverPosition,
            icon: driverIcon,
            rotation: rotation,
            infoWindow: InfoWindow(
              title:
                  'Driver: ${orderData.assignedDriver?.firstName ?? ""} ${orderData.assignedDriver?.lastName ?? ""}',
            ),
            zIndex: 2,
          ),
        );
      }

      // Customer marker
      if (orderData.address?.location?.coordinates != null &&
          orderData.address!.location!.coordinates!.length >= 2) {
        markers.add(
          Marker(
            markerId: const MarkerId("customer"),
            position: LatLng(
              orderData.address!.location!.coordinates![1],
              orderData.address!.location!.coordinates![0],
            ),
            icon: customerIcon,
            infoWindow: const InfoWindow(title: "Customer"),
          ),
        );
      }
    } catch (e) {
      print('Error creating markers: $e');
    }

    return markers;
  }

  Future<BitmapDescriptor> getResizedMarker(String path, int width) async {
    final ByteData data = await rootBundle.load(path);
    final codec = await ui.instantiateImageCodec(
      data.buffer.asUint8List(),
      targetWidth: width,
      allowUpscaling: false,
    );
    final frame = await codec.getNextFrame();
    final bytes = await frame.image.toByteData(format: ui.ImageByteFormat.png);
    return BitmapDescriptor.bytes(bytes!.buffer.asUint8List());
  }

  int? _getChefIdFromLastReorder() {
    return _lastReorderChefId;
  }

  void _showReorderErrorDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: EdgeInsets.all(screenWidth * 0.06),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Error icon
                Container(
                  width: screenWidth * 0.15,
                  height: screenWidth * 0.15,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFEBE7),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.error_outline,
                    color: const Color(0xFFD31510),
                    size: screenWidth * 0.08,
                  ),
                ),
                SizedBox(height: screenHeight * 0.02),
                
                // Title
                Text(
                  'Reorder Failed',
                  style: TextStyle(
                    fontSize: twenty,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'Inter',
                    color: const Color(0xFF1F2122),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenHeight * 0.015),
                
                // Message
                Text(
                  message,
                  style: TextStyle(
                    fontSize: forteen,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    color: const Color(0xFF66696D),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: screenHeight * 0.025),
                
                // OK Button
                SizedBox(
                  width: double.infinity,
                  height: screenHeight * 0.055,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1F2122),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: sixteen,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Inter',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
