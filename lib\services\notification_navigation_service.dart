import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:db_eats/main.dart';
import 'package:db_eats/ui/orders/confirmed_orders_view.dart';
import 'package:db_eats/ui/catering/cateringrequest.dart';

class NotificationNavigationService {
  static Map<String, dynamic>? _pendingNotificationData;
  static bool _isNavigating = false;
  static bool _navigationCompleted = false;

  /// Handles navigation when a notification is clicked
  static void handleNotificationClick(OSNotification notification) {
    try {
      log('=== NOTIFICATION NAVIGATION START ===');
      log('Processing notification click: ${notification.jsonRepresentation()}');
      log('Current navigation state - isNavigating: $_isNavigating, navigationCompleted: $_navigationCompleted');

      // Parse the custom data from the notification
      final customData = _parseCustomData(notification);
      if (customData == null) {
        log('No custom data found in notification');
        return;
      }

      // Extract notification type and target ID
      final int? notificationType = customData['type'];
      final int? targetId = customData['target_id'];
      final int? userId = customData['user_id'];
      final String? userRole = customData['user_role'];

      log('Notification type: $notificationType, Target ID: $targetId, User ID: $userId, User Role: $userRole');

      // Store notification data for delayed navigation if context is not available
      _pendingNotificationData = {
        'type': notificationType,
        'target_id': targetId,
        'user_id': userId,
        'user_role': userRole,
      };

      log('Stored pending notification data, attempting navigation...');
      // Try immediate navigation
      _attemptNavigation();
    } catch (e) {
      log('Error handling notification click: $e');
    }
  }

  /// Attempts to navigate using the current context or schedules for later
  static void _attemptNavigation() {
    if (_isNavigating) {
      log('Navigation already in progress, skipping');
      return;
    }

    final navigatorState = NavigationService.navigatorKey.currentState;
    if (navigatorState == null || !navigatorState.mounted) {
      log('Navigator state not ready, scheduling delayed navigation');
      _scheduleDelayedNavigation();
      return;
    }

    if (_pendingNotificationData == null) {
      log('No pending notification data');
      return;
    }

    _isNavigating = true;

    // Add a small delay to ensure the app is fully loaded
    Future.delayed(const Duration(milliseconds: 500), () {
      try {
        final navigatorState = NavigationService.navigatorKey.currentState;
        if (navigatorState != null &&
            navigatorState.mounted &&
            _pendingNotificationData != null) {
          _navigateBasedOnType(
            navigatorState.context,
            _pendingNotificationData!['type'],
            _pendingNotificationData!['target_id'],
          );
        } else {
          log('Navigator state not ready or not mounted, skipping navigation');
        }
      } catch (e) {
        log('Error in delayed navigation: $e');
      } finally {
        _isNavigating = false;
        _pendingNotificationData = null;
      }
    });
  }

  /// Schedules navigation for when context becomes available
  static void _scheduleDelayedNavigation() {
    int attempts = 0;
    const maxAttempts = 10;
    const delayBetweenAttempts = Duration(milliseconds: 500);

    void tryNavigation() {
      attempts++;
      final navigatorState = NavigationService.navigatorKey.currentState;

      if (navigatorState != null && navigatorState.mounted) {
        log('Navigator state available after $attempts attempts, proceeding with navigation');
        _attemptNavigation();
        return;
      }

      if (attempts < maxAttempts) {
        log('Navigator state still not ready, attempt $attempts/$maxAttempts, retrying...');
        Future.delayed(delayBetweenAttempts, tryNavigation);
      } else {
        log('Max attempts reached, giving up on notification navigation');
        _pendingNotificationData = null;
        _isNavigating = false;
      }
    }

    tryNavigation();
  }

  /// Called when the app becomes ready to handle navigation
  static void onAppReady() {
    if (_pendingNotificationData != null &&
        !_isNavigating &&
        !_navigationCompleted) {
      log('App ready, processing pending notification');
      _attemptNavigation();
    }
  }

  /// Check if navigation is currently protected
  static bool get isNavigationProtected => _navigationCompleted;

  /// Check if notification navigation is currently in progress
  static bool get isNavigationInProgress => _isNavigating;

  /// Parse custom data from notification
  static Map<String, dynamic>? _parseCustomData(OSNotification notification) {
    try {
      log('Parsing notification data...');
      log('AdditionalData: ${notification.additionalData}');
      log('RawPayload: ${notification.rawPayload}');

      // Try to get custom data from additionalData first
      if (notification.additionalData != null &&
          notification.additionalData!.isNotEmpty) {
        log('Using additionalData: ${notification.additionalData}');
        return notification.additionalData!;
      }

      // If additionalData is empty, try to parse from rawPayload
      if (notification.rawPayload != null) {
        Map<String, dynamic> payload;
        if (notification.rawPayload is String) {
          payload = jsonDecode(notification.rawPayload as String)
              as Map<String, dynamic>;
        } else if (notification.rawPayload is Map<String, dynamic>) {
          payload = notification.rawPayload as Map<String, dynamic>;
        } else {
          log('Unknown rawPayload type: ${notification.rawPayload.runtimeType}');
          return null;
        }

        log('Parsed payload: $payload');

        // Check if there's a 'custom' field that needs to be parsed
        if (payload.containsKey('custom') && payload['custom'] is String) {
          final customString = payload['custom'] as String;
          log('Custom string: $customString');
          final customData = jsonDecode(customString);
          log('Parsed custom data: $customData');

          // Extract the 'a' field which contains the actual data
          if (customData is Map<String, dynamic> &&
              customData.containsKey('a')) {
            final result = Map<String, dynamic>.from(customData['a']);
            log('Extracted data from "a" field: $result');
            return result;
          }
        }
      }

      log('No custom data found');
      return null;
    } catch (e) {
      log('Error parsing custom data: $e');
      return null;
    }
  }

  /// Navigate to appropriate page based on notification type
  static void _navigateBasedOnType(
      BuildContext context, int? notificationType, int? targetId) {
    if (notificationType == null) {
      log('Notification type is null, cannot determine navigation');
      return;
    }

    switch (notificationType) {
      case 2: // Order confirmed
        log('Navigating to order details for confirmed order: $targetId');
        _navigateToOrderDetails(context, targetId);
        break;

      case 3: // Order status update (delivered, cancelled, etc.)
        log('Navigating to order details for status update: $targetId');
        _navigateToOrderDetails(context, targetId);
        break;

      case 4: // Order preparing/picking up/in transit
        log('Navigating to order details for order in progress: $targetId');
        _navigateToOrderDetails(context, targetId);
        break;

      case 13: // Catering request accepted
        log('Navigating to catering requests - accepted tab');
        _navigateToCateringRequests(context, 1); // Accepted tab
        break;

      case 14: // Catering request completed
        log('Navigating to catering requests - past orders tab');
        _navigateToCateringRequests(context, 2); // Past Orders tab
        break;

      default:
        log('Unknown notification type: $notificationType');
        _showUnknownNotificationDialog(context, notificationType);
        break;
    }
  }

  /// Navigate to order details page
  static void _navigateToOrderDetails(BuildContext context, int? orderId) {
    if (orderId == null || orderId == 0) {
      log('Invalid order ID: $orderId');
      return;
    }

    try {
      // Use regular push to maintain navigation stack
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ConfirmedOrdersView(
            orderId: orderId,
          ),
        ),
      ).then((_) {
        // Mark navigation as completed and add delay to prevent interference
        log('Navigation to order details completed, setting protection for 5 seconds');
        _navigationCompleted = true;
        Future.delayed(const Duration(seconds: 5), () {
          _navigationCompleted = false;
          log('Navigation protection removed');
        });
      });
      log('Successfully navigated to order details for order: $orderId');
      log('=== NOTIFICATION NAVIGATION END ===');
    } catch (e) {
      log('Error navigating to order details: $e');
    }
  }

  /// Navigate to catering requests page
  static void _navigateToCateringRequests(
      BuildContext context, int initialTab) {
    try {
      // Use regular push to maintain navigation stack
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CateringRequestsPage(
            initialTab: initialTab,
          ),
        ),
      ).then((_) {
        // Mark navigation as completed and add delay to prevent interference
        _navigationCompleted = true;
        Future.delayed(const Duration(seconds: 2), () {
          _navigationCompleted = false;
        });
      });
      log('Successfully navigated to catering requests with tab: $initialTab');
    } catch (e) {
      log('Error navigating to catering requests: $e');
    }
  }

  /// Show dialog for unknown notification types
  static void _showUnknownNotificationDialog(
      BuildContext context, int? notificationType) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification'),
          content: Text(
              'Received notification with unknown type: $notificationType'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Handle notification received in foreground (optional)
  static void handleForegroundNotification(OSNotification notification) {
    log('Notification received in foreground: ${notification.jsonRepresentation()}');

    // You can add custom logic here for foreground notifications
    // For example, show an in-app notification banner or update UI
  }
}
