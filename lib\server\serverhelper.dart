import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:db_eats/storage/localstorage.dart';
import 'package:http/http.dart' as http;

class ServerException implements Exception {
  final String message;
  ServerException(this.message);
}

class ServerHelper {
  ServerHelper._();

  /// API Configuration
//https://eatro-api.evoqins.dev
  // static const String _testtUrl = 'https://eatro-api.evoqins.dev';
  static const String _testtUrl = 'http://192.168.62.76:5055';
  static const String _cyberhawkUrl = 'http://192.168.36.208:5055';

  static String get baseUrl => _cyberhawkUrl;
  static String get baseUrl1 => _testtUrl;

  static String get imageUrl => '$_testtUrl/file/get/?key=';

  static Future<Map<String, String>> get _headers async {
    final token = await LocalStorage.getFcmToken();
    return {"Content-Type": "application/json", "token": token ?? ""};
  }

  /// Uploads a file to the server
  static Future<Map<String, dynamic>> uploadFile(
    String route,
    String fieldName,
    File file,
  ) async {
    try {
      final uri = Uri.parse('$_testtUrl$route');
      final request = http.MultipartRequest('POST', uri);
      final token = await LocalStorage.getAccessToken();

      final multipartFile = http.MultipartFile.fromBytes(
        fieldName,
        file.readAsBytesSync(),
        filename: file.path,
      );

      request.files.add(multipartFile);
      request.headers.addAll({
        "Content-Type": "application/json",
        "access-token": token ?? "",
      });

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      return jsonDecode(response.body) as Map<String, dynamic>;
    } catch (e) {
      log('File upload error: $e');
      throw ServerException('File upload failed: $e');
    }
  }

  /// Uploads multiple files to the server
  static Future<Map<String, dynamic>> uploadFiles(
    String endpoint,
    String fieldName,
    List<File> files, {
    Map<String, String>? additionalData,
  }) async {
    var uri = Uri.parse(_testtUrl + endpoint);
    var request = http.MultipartRequest('POST', uri);

    // Get access token
    final token = await LocalStorage.getAccessToken();

    // Add authorization header with correct format
    request.headers['access-token'] = token ?? '';
    request.headers['Content-Type'] = 'multipart/form-data';

    // Add all text fields
    if (additionalData != null) {
      request.fields.addAll(additionalData);
    }

    // Add all files
    for (var file in files) {
      var stream = http.ByteStream(file.openRead());
      var length = await file.length();
      var multipartFile = http.MultipartFile(
        fieldName,
        stream,
        length,
        filename: file.path.split('/').last,
      );
      request.files.add(multipartFile);
    }

    var response = await request.send();
    var responseData = await response.stream.bytesToString();
    return json.decode(responseData);
  }

  /// Checks if the internet connection is good
  static Future<bool> isInternetConnectionGood() async {
    try {
      final response = await http.get(
        Uri.parse('${ServerHelper.baseUrl}/health'),
        headers: {"Content-Type": "application/json", "token": ""},
      );
      return response.statusCode == 200;
    } catch (e) {
      log('checking connection error $e');
      return false;
    }
  }

  /// Sends a POST request to the server
  static Future<dynamic> post1(String url, Map<String, dynamic> data) async {
    final token = await LocalStorage.getAccessToken();
    log(token ?? "");
    log('${baseUrl1 + url} -- $data');
    final body = json.encode(data);
    try {
      final response = await http
          .post(Uri.parse(baseUrl1 + url),
              headers: {
                "Content-Type": "application/json",
                "access-token": token ?? ""
              },
              body: body)
          .timeout(const Duration(seconds: 20));
      log('${baseUrl1 + url} -- $data');
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return jsonDecode(response.body);

        // return {

        //   "status": false,
        //   "msg": "${response.statusCode} - ${response.reasonPhrase}"
        // };
      }
    } catch (e) {
      log(e.toString());
      throw ServerException('POST request failed: $e');
    }
  }

  /// Sends a GET request to the server
  static Future<dynamic> get1(String url) async {
    try {
      final token = await LocalStorage.getAccessToken();
      log(token.toString());
      final response = await http.get(
        Uri.parse(baseUrl1 + url),
        headers: {
          "Content-Type": "application/json",
          "access-token": token ?? ""
        },
      );
      log(baseUrl1 + url);
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return jsonDecode(response.body);
        //  {
        //   // "status": false,
        //   // "msg": "${response.statusCode} - ${response.reasonPhrase}"
        // };
      }
    } catch (e) {
      log(e.toString());
      throw ServerException('GET request failed: $e');
    }
  }

  static Future<dynamic> getrefresh(String url) async {
    try {
      final token = await LocalStorage.getAccessToken();
      final token1 = await LocalStorage.getRefreshToken();
      log(token1.toString());
      final response = await http.get(
        Uri.parse(baseUrl1 + url),
        headers: {
          "Content-Type": "application/json",
          "refresh-token": token1 ?? ""
        },
      );
      log(baseUrl1 + url);
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return jsonDecode(response.body);
        //  {
        //   // "status": false,
        //   // "msg": "${response.statusCode} - ${response.reasonPhrase}"
        // };
      }
    } catch (e) {
      log(e.toString());
      throw ServerException('GET request failed: $e');
    }
  }

  /// Sends a POST request to the server
  static Future<dynamic> post(String url, Map<String, dynamic> data) async {
    final token = await LocalStorage.getFcmToken();
    log(token ?? "");
    log('${baseUrl + url} -- $data');
    final body = json.encode(data);
    try {
      final response = await http
          .post(Uri.parse(baseUrl + url),
              headers: {
                "Content-Type": "application/json",
                "access-token": token ?? ""
              },
              body: body)
          .timeout(const Duration(seconds: 20));
      log('${baseUrl + url} -- $data');
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return {
          "status": false,
          "msg": "${response.statusCode} - ${response.reasonPhrase}"
        };
      }
    } catch (e) {
      log(e.toString());
      throw ServerException('POST request failed: $e');
    }
  }

  /// Sends a GET request to the server
  static Future<dynamic> get(String url) async {
    try {
      final token = await LocalStorage.getFcmToken();
      log(token.toString());
      final response = await http.get(
        Uri.parse(baseUrl + url),
        headers: {
          "Content-Type": "application/json",
          "access-token": token ?? ""
        },
      );
      log(baseUrl + url);
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return {
          "status": false,
          "msg": "${response.statusCode} - ${response.reasonPhrase}"
        };
      }
    } catch (e) {
      log(e.toString());
      throw ServerException('GET request failed: $e');
    }
  }

  /// Downloads a file from the server
  static Future<Uint8List?> getDownload(String url) async {
    try {
      final token = await LocalStorage.getFcmToken();
      final response = await http.get(
        Uri.parse(baseUrl + url),
        headers: {
          "Content-Type": "application/json",
          "access-token": token ?? ""
        },
      );
      log(baseUrl + url);
      if (response.statusCode == 200) {
        return response.bodyBytes;
      } else {
        return null;
      }
    } catch (e) {
      log(e.toString());
      throw ServerException('File download failed: $e');
    }
  }
}
