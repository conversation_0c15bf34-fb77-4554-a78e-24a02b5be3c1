import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/common/initializer.dart';
import 'package:db_eats/services/notification_navigation_service.dart';
import 'package:db_eats/storage/localstorage.dart';
import 'package:db_eats/ui/home.dart';
import 'package:db_eats/ui/mainnavigationpage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;

// Google Maps API key
const String kGoogleApiKey = "AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY";

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with WidgetsBindingObserver {
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _zipCodeController = TextEditingController();
  final FocusNode _addressFocusNode = FocusNode();
  bool _isLoading = false;
  String? _selectedPlaceId;
  String? _selectedAddress;
  double? _selectedLatitude;
  double? _selectedLongitude;
  List<Prediction> _searchResults = [];
  bool _showPredictions = false;
  bool _isDropdownActive = false;
  bool _isKeyboardVisible = false;

  // Add these for debouncing and request management
  Timer? _debounceTimer;
  String _lastSearchQuery = '';
  http.Client? _httpClient;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _httpClient = http.Client(); // Initialize HTTP client
    _checkSavedAddress();

    // Listen to focus changes
    _addressFocusNode.addListener(() {
      if (!_addressFocusNode.hasFocus) {
        // Close dropdown when focus is lost
        _closeDropdown();
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _addressController.dispose();
    _zipCodeController.dispose();
    _addressFocusNode.dispose();
    _debounceTimer?.cancel(); // Cancel debounce timer
    _httpClient?.close(); // Close HTTP client
    super.dispose();
  }

  // Monitor keyboard visibility changes
  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = bottomInset > 0;

    if (_isKeyboardVisible && !isKeyboardVisible) {
      // Keyboard was closed
      _closeDropdown();
    }

    _isKeyboardVisible = isKeyboardVisible;
  }

  // Helper method to close dropdown
  void _closeDropdown() {
    if (_showPredictions) {
      setState(() {
        _showPredictions = false;
        _searchResults = [];
        _isDropdownActive = false;
      });
    }
  }

  Future<void> _checkSavedAddress() async {
    await Future.delayed(const Duration(seconds: 2));

    // Check if notification navigation is in progress
    if (NotificationNavigationService.isNavigationProtected) {
      log('Notification navigation in progress, skipping automatic navigation');
      return;
    }

    final token = await LocalStorage.getAccessToken();

    if (token != null && token.isNotEmpty && mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainNavigationScreen()),
      );
    }
  }

  Future<void> _handleDeliverHere() async {
    setState(() => _isLoading = true);

    try {
      // Check if notification navigation is in progress
      if (NotificationNavigationService.isNavigationProtected) {
        log('Notification navigation in progress, skipping deliver here navigation');
        setState(() => _isLoading = false);
        return;
      }

      final token = await LocalStorage.getAccessToken();

      if (token != null && token.isNotEmpty) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainNavigationScreen()),
        );
      } else {
        if (_addressController.text.trim().isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please enter or select an address')),
          );
          return;
        }

        if (_selectedAddress == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please select a valid address')),
          );
          return;
        }

        await Initializer.saveAddress(_selectedAddress!);
        if (_selectedLatitude != null && _selectedLongitude != null) {
          final initializer = Initializer();
          initializer.setCoordinates(_selectedLatitude!, _selectedLongitude!);
        }

        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const Home()),
        );
      }
    } catch (e) {
      log('Error handling delivery: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() => _isLoading = true);

    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permissions are denied')),
          );
          setState(() => _isLoading = false);
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text(
                  'Location permissions are permanently denied, please enable in settings')),
        );
        setState(() => _isLoading = false);
        return;
      }

      Position position = await Geolocator.getCurrentPosition();

      double lat = position.latitude;
      double lon = position.longitude;
      Initializer.latitude = lat.toString();
      Initializer.longitude = lon.toString();
      log('Latitude: ${Initializer.latitude}, Longitude: ${Initializer.longitude}');

      List<Placemark> placemarks = await placemarkFromCoordinates(lat, lon);

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address = '';

        if (place.street != null && place.street!.isNotEmpty) {
          address += place.street!;
        }

        if (place.locality != null && place.locality!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.locality!;
        }

        if (place.administrativeArea != null &&
            place.administrativeArea!.isNotEmpty) {
          if (address.isNotEmpty) address += ', ';
          address += place.administrativeArea!;
        }

        setState(() {
          _selectedAddress = address;
          _selectedLatitude = lat;
          _selectedLongitude = lon;
          _addressController.text = address;
          _zipCodeController.text = place.postalCode ?? '';
          _showPredictions = false;
          _searchResults = [];
          _isLoading = false;
        });
      } else {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('No address found for current location')),
        );
      }
    } catch (e) {
      log('Error getting location: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error getting location: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  // New debounced search method
  void _debouncedSearchPlaces(String query) {
    // Cancel the previous timer
    _debounceTimer?.cancel();

    // If query is empty, immediately close dropdown
    if (query.trim().isEmpty) {
      _closeDropdown();
      _lastSearchQuery = '';
      return;
    }

    // Set a new timer
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted && query.trim().isNotEmpty) {
        _performSearch(query);
      }
    });
  }

  // Separate method to perform the actual search
  Future<void> _performSearch(String query) async {
    // Store the current query to check if it's still valid when response comes
    _lastSearchQuery = query;

    try {
      final response = await _httpClient!.get(
        Uri.parse('https://maps.googleapis.com/maps/api/place/autocomplete/json'
            '?input=$query'
            '&types=address'
            '&key=$kGoogleApiKey'),
      );

      // Check if the query is still the same (user hasn't typed more)
      if (!mounted || _lastSearchQuery != query) {
        return;
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          final results = (data['predictions'] as List)
              .map((p) => Prediction(
                    p['description'],
                    p['place_id'],
                    p['structured_formatting']?['main_text'],
                  ))
              .toList();

          // Only update UI if this is still the current query
          if (mounted && _lastSearchQuery == query) {
            setState(() {
              _searchResults = results;
              _showPredictions = _searchResults.isNotEmpty;
              _isDropdownActive = _searchResults.isNotEmpty;
            });
          }
        } else if (data['status'] == 'ZERO_RESULTS') {
          if (mounted && _lastSearchQuery == query) {
            setState(() {
              _searchResults = [];
              _showPredictions = true;
              _isDropdownActive = true;
            });
          }
        } else {
          if (mounted && _lastSearchQuery == query) {
            _closeDropdown();
            throw Exception('Places API error: ${data['status']}');
          }
        }
      } else {
        if (mounted && _lastSearchQuery == query) {
          _closeDropdown();
          throw Exception('Failed to fetch places: ${response.statusCode}');
        }
      }
    } catch (e) {
      log('Error searching places: $e');
      if (mounted && _lastSearchQuery == query) {
        _closeDropdown();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error searching places: $e')),
        );
      }
    }
  }

  // Keep the old method for backward compatibility but make it use the new debounced version
  Future<void> searchPlaces(String query) async {
    _debouncedSearchPlaces(query);
  }

  Future<void> selectPlace(Prediction prediction) async {
    setState(() => _isLoading = true);

    try {
      final response = await _httpClient!.get(
        Uri.parse('https://maps.googleapis.com/maps/api/place/details/json'
            '?place_id=${prediction.placeId}'
            '&fields=formatted_address,geometry'
            '&key=$kGoogleApiKey'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          final location = data['result']['geometry']['location'];
          final lat = location['lat'];
          final lng = location['lng'];
          final address = data['result']['formatted_address'];

          setState(() {
            _selectedAddress = address;
            _selectedLatitude = lat;
            _selectedLongitude = lng;
            _addressController.text = address;
            _showPredictions = false;
            _searchResults = [];
            _selectedPlaceId = prediction.placeId;
            _isLoading = false;
            _isDropdownActive = false;
          });
        } else {
          throw Exception('Place details API error: ${data['status']}');
        }
      } else {
        // Commented out exception throw as in original code
        // throw Exception(
        //     'Failed to fetch place details: ${response.statusCode}');
      }
    } catch (e) {
      log('Error getting place details: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error getting place details: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  double _getResponsiveFontSize(BuildContext context, double size) {
    double screenWidth = MediaQuery.of(context).size.width;
    double baseWidth = 375.0;
    double scaleFactor = screenWidth / baseWidth;
    double scaledSize = size * scaleFactor;
    return scaledSize.clamp(size * 0.8, size * 1.2);
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return BlocListener<AccountBloc, AccountState>(
      listener: (context, state) {
        if (state is AddCurrentAddressSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Address saved successfully')),
          );
        } else if (state is AddCurrentAddressFailed) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to save address: ${state.message}')),
          );
        }
      },
      child: GestureDetector(
        onTap: () {
          // Close dropdown when tapping outside
          FocusScope.of(context).unfocus();
          _closeDropdown();
        },
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          body: Stack(
            children: [
              // Background image - Fixed: Positioned.fill as direct child of Stack
              Positioned.fill(
                child: IgnorePointer(
                  child: Image.asset(
                    'assets/images/splashscreen_bg.png',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                    alignment: Alignment.center,
                  ),
                ),
              ),
              Positioned(
                top: MediaQuery.of(context).padding.top - screenHeight * 0.005,
                left: 16,
                child: SafeArea(
                  child: Image.asset(
                    'assets/logow.png',
                    width: (screenHeight * 0.005) * 37.22,
                    height: (screenHeight * 0.005) * 10,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              SafeArea(
                child: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height -
                          MediaQuery.of(context).padding.top -
                          MediaQuery.of(context).padding.bottom,
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: MediaQuery.of(context).size.width * 0.06,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                              height:
                                  MediaQuery.of(context).size.height * 0.44),
                          Text(
                            'Home-Cooked\nComfort,\nWherever You Are',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: ten * 4.3,
                              fontWeight: FontWeight.w400,
                              fontFamily: "Inter",
                              height: 1.1,
                              letterSpacing: -1,
                            ),
                          ),
                          SizedBox(
                              height:
                                  MediaQuery.of(context).size.height * 0.02),
                          Text(
                            'Enjoy fresh, preservative-free dishes\nprepared by local home chefs delivered\nright to your door. Experience the taste\nof home on the go.',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: _getResponsiveFontSize(context, 18),
                              fontWeight: FontWeight.w400,
                              height: 1.4,
                            ),
                          ),
                          SizedBox(
                              height:
                                  MediaQuery.of(context).size.height * 0.054),
                          FutureBuilder<List<dynamic>>(
                            future: Future.wait([
                              LocalStorage.getAccessToken(),
                              Initializer.getSavedAddress(),
                            ]),
                            builder: (context, snapshot) {
                              if (!snapshot.hasData) {
                                return Container();
                              }

                              final token = snapshot.data![0] as String?;
                              final savedAddress = snapshot.data![1] as String?;

                              if (token != null && token.isNotEmpty) {
                                return Container();
                              }

                              return Column(
                                children: [
                                  _buildAddressInputOnly(),
                                  SizedBox(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.018),
                                  _buildDeliverButton(),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // Dropdown overlay positioned outside the scrollable area
              if (_showPredictions) _buildDropdownOverlay(),
            ],
          ),
        ),
      ),
    );
  }
  // @override
  // Widget build(BuildContext context) {
  //   final screenHeight = MediaQuery.of(context).size.height;
  //   final screenWidth = MediaQuery.of(context).size.width;

  //   return BlocListener<AccountBloc, AccountState>(
  //     listener: (context, state) {
  //       if (state is AddCurrentAddressSuccess) {
  //         ScaffoldMessenger.of(context).showSnackBar(
  //           const SnackBar(content: Text('Address saved successfully')),
  //         );
  //       } else if (state is AddCurrentAddressFailed) {
  //         setState(() => _isLoading = false);
  //         ScaffoldMessenger.of(context).showSnackBar(
  //           SnackBar(content: Text('Failed to save address: ${state.message}')),
  //         );
  //       }
  //     },
  //     child: GestureDetector(
  //       onTap: () {
  //         // Close dropdown when tapping outside
  //         FocusScope.of(context).unfocus();
  //         _closeDropdown();
  //       },
  //       child: Scaffold(
  //         resizeToAvoidBottomInset: true,
  //         body: Stack(
  //           children: [
  //             // Background image, ignoring pointer events
  //             IgnorePointer(
  //               child: Positioned.fill(
  //                 child: Image.asset(
  //                   'assets/images/splashscreen_bg.png',
  //                   fit: BoxFit.cover,
  //                   width: double.infinity,
  //                   height: double.infinity,
  //                   alignment: Alignment.center,
  //                 ),
  //               ),
  //             ),
  //             Positioned(
  //               top: MediaQuery.of(context).padding.top - screenHeight * 0.005,
  //               left: 16,
  //               child: SafeArea(
  //                 child: Image.asset(
  //                   'assets/logow.png',
  //                   width: (screenHeight * 0.005) * 37.22,
  //                   height: (screenHeight * 0.005) * 10,
  //                   fit: BoxFit.contain,
  //                 ),
  //               ),
  //             ),
  //             SafeArea(
  //               child: SingleChildScrollView(
  //                 physics: const ClampingScrollPhysics(),
  //                 child: ConstrainedBox(
  //                   constraints: BoxConstraints(
  //                     minHeight: MediaQuery.of(context).size.height -
  //                         MediaQuery.of(context).padding.top -
  //                         MediaQuery.of(context).padding.bottom,
  //                   ),
  //                   child: Padding(
  //                     padding: EdgeInsets.symmetric(
  //                       horizontal: MediaQuery.of(context).size.width * 0.06,
  //                     ),
  //                     child: Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       mainAxisSize: MainAxisSize.min,
  //                       children: [
  //                         SizedBox(
  //                             height: MediaQuery.of(context).size.height * 0.44),
  //                         Text(
  //                           'Home-Cooked\nComfort,\nWherever You Are',
  //                           style: TextStyle(
  //                             color: Colors.white,
  //                             fontSize: ten * 4.3,
  //                             fontWeight: FontWeight.w400,
  //                             fontFamily: "Inter",
  //                             height: 1.1,
  //                             letterSpacing: -1,
  //                           ),
  //                         ),
  //                         SizedBox(
  //                             height: MediaQuery.of(context).size.height * 0.02),
  //                         Text(
  //                           'Enjoy fresh, preservative-free dishes\nprepared by local home chefs delivered\nright to your door. Experience the taste\nof home on the go.',
  //                           style: TextStyle(
  //                             color: Colors.white,
  //                             fontSize: _getResponsiveFontSize(context, 18),
  //                             fontWeight: FontWeight.w400,
  //                             height: 1.4,
  //                           ),
  //                         ),
  //                         SizedBox(
  //                             height: MediaQuery.of(context).size.height * 0.054),
  //                         FutureBuilder<List<dynamic>>(
  //                           future: Future.wait([
  //                             LocalStorage.getAccessToken(),
  //                             Initializer.getSavedAddress(),
  //                           ]),
  //                           builder: (context, snapshot) {
  //                             if (!snapshot.hasData) {
  //                               return Container();
  //                             }

  //                             final token = snapshot.data![0] as String?;
  //                             final savedAddress = snapshot.data![1] as String?;

  //                             if (token != null && token.isNotEmpty) {
  //                               return Container();
  //                             }

  //                             return Column(
  //                               children: [
  //                                 _buildAddressInputOnly(),
  //                                 SizedBox(
  //                                     height: MediaQuery.of(context).size.height *
  //                                         0.018),
  //                                 _buildDeliverButton(),
  //                               ],
  //                             );
  //                           },
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //             ),
  //             // Dropdown overlay positioned outside the scrollable area
  //             if (_showPredictions)
  //               _buildDropdownOverlay(),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildAddressInputOnly() {
    final screenSize = MediaQuery.of(context).size;

    return Container(
      height: screenSize.height * 0.05,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        border: _showPredictions
            ? Border.all(color: const Color(0xFFFFBE16), width: 2)
            : null,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Padding(
              padding:
                  EdgeInsets.symmetric(horizontal: screenSize.width * 0.05),
              child: TextField(
                controller: _addressController,
                focusNode: _addressFocusNode,
                maxLines: 1,
                textAlign: TextAlign.start,
                decoration: InputDecoration(
                  hintText: 'Enter Address or Zip code',
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                    color: Colors.grey,
                    fontWeight: FontWeight.w400,
                    fontSize: _getResponsiveFontSize(context, 16),
                    overflow: TextOverflow.ellipsis,
                  ),
                  isCollapsed: true,
                  contentPadding: EdgeInsets.zero,
                ),
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: _getResponsiveFontSize(context, 16),
                  overflow: TextOverflow.ellipsis,
                ),
                onChanged: (value) {
                  // Use the debounced search method
                  _debouncedSearchPlaces(value);
                },
                onTap: () {
                  if (_searchResults.isNotEmpty) {
                    setState(() {
                      _showPredictions = true;
                      _isDropdownActive = true;
                    });
                  }
                },
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: screenSize.width * 0.04),
            child: GestureDetector(
              onTap: _getCurrentLocation,
              child: Row(
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.black54),
                            ),
                          )
                        : Image.asset(
                            'assets/icons/location2.png',
                            width: _getResponsiveFontSize(context, 20),
                            height: _getResponsiveFontSize(context, 20),
                            fit: BoxFit.contain,
                          ),
                  ),
                  SizedBox(width: screenSize.width * 0.02),
                  IntrinsicWidth(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Locate me',
                          style: TextStyle(
                            fontSize: _getResponsiveFontSize(context, 14),
                            fontWeight: FontWeight.w400,
                            decoration: TextDecoration.none,
                          ),
                        ),
                        const SizedBox(height: 0),
                        Container(
                          height: 1,
                          width: double.infinity,
                          color: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownOverlay() {
    final screenSize = MediaQuery.of(context).size;
    final screenPadding = MediaQuery.of(context).padding;

    // Calculate the position based on where the input field should be
    final isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 0;
    final topPosition = screenPadding.top * (isKeyboardVisible ? 2.6 : 4.5);

    return Positioned(
      bottom: topPosition,
      left: screenSize.width * 0.06,
      right: screenSize.width * 0.06,
      child: Material(
        elevation: 12,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: screenSize.height * 0.3,
            minHeight: 50,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border:
                        Border(bottom: BorderSide(color: Colors.grey.shade200)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.location_on,
                          size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 8),
                      Text(
                        'Select Address',
                        style: TextStyle(
                          fontSize: _getResponsiveFontSize(context, 12),
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                // Predictions list with independent scrolling
                Flexible(
                  child: _searchResults.isEmpty
                      ? Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.search_off,
                                size: 20,
                                color: Colors.grey.shade500,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'No addresses found',
                                style: TextStyle(
                                  fontSize: _getResponsiveFontSize(context, 14),
                                  color: Colors.grey.shade600,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        )
                      : NotificationListener<ScrollNotification>(
                          onNotification: (ScrollNotification notification) {
                            return true;
                          },
                          child: MediaQuery.removePadding(
                            context: context,
                            removeTop: true,
                            child: ListView.builder(
                              shrinkWrap: true,
                              physics: const BouncingScrollPhysics(),
                              itemCount: _searchResults.length,
                              itemBuilder: (context, index) {
                                final prediction = _searchResults[index];
                                return Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      print(
                                          'Tapped prediction: ${prediction.description}');
                                      FocusScope.of(context).unfocus();
                                      selectPlace(prediction);
                                      setState(() {
                                        _showPredictions = false;
                                        _isDropdownActive = false;
                                      });
                                    },
                                    splashColor: Colors.grey.withOpacity(0.2),
                                    highlightColor:
                                        Colors.grey.withOpacity(0.1),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 12),
                                      decoration: BoxDecoration(
                                        border: Border(
                                          bottom: index <
                                                  _searchResults.length - 1
                                              ? BorderSide(
                                                  color: Colors.grey.shade200,
                                                  width: 0.5)
                                              : BorderSide.none,
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.location_on_outlined,
                                            size: 18,
                                            color: Colors.grey.shade600,
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                if (prediction.mainText != null)
                                                  Text(
                                                    prediction.mainText!,
                                                    style: TextStyle(
                                                      fontSize:
                                                          _getResponsiveFontSize(
                                                              context, 14),
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                Text(
                                                  prediction.description ?? '',
                                                  style: TextStyle(
                                                    fontSize:
                                                        _getResponsiveFontSize(
                                                            context, 12),
                                                    color: Colors.grey.shade600,
                                                  ),
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeliverButton() {
    final screenSize = MediaQuery.of(context).size;

    return SizedBox(
      width: double.infinity,
      height: screenSize.height * 0.05,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleDeliverHere,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFBE16),
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                ),
              )
            : Text(
                'Deliver Here',
                style: TextStyle(
                  fontSize: _getResponsiveFontSize(context, 16),
                  fontFamily: 'Inter-medium',
                ),
              ),
      ),
    );
  }
}

class Prediction {
  final String? description;
  final String? placeId;
  final String? mainText;

  Prediction(this.description, this.placeId, this.mainText);
}
