import 'package:db_eats/ui/profile/modifyaddress.dart' as modify_address;
import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/data/models/guesthome/listaddressmodel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SavedAddressPage extends StatefulWidget {
  const SavedAddressPage({super.key});

  @override
  _SavedAddressPageState createState() => _SavedAddressPageState();
}

class _SavedAddressPageState extends State<SavedAddressPage> {
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;

  List<AddressData>? addresses;
  bool isLoading = true;
  int? deletingAddressId;
  int? makingCurrentAddressId;
  @override
  void initState() {
    super.initState();
    context.read<AccountBloc>().add(ListAddressesEvent());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;
    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AccountBloc, AccountState>(
      listener: (context, state) {
        if (state is ListAddressesSuccess) {
          setState(() {
            addresses = state.data;
            isLoading = false;
          });
        } else if (state is ListAddressesFailed) {
          setState(() {
            isLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is ListAddressesLoading) {
          setState(() {
            isLoading = true;
          });
        } else if (state is DeleteAddressLoading) {
        } else if (state is DeleteAddressSuccess) {
          setState(() {
            deletingAddressId = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
          context.read<AccountBloc>().add(ListAddressesEvent());
        } else if (state is DeleteAddressFailed) {
          setState(() {
            deletingAddressId = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is EditAddressLoading) {
          // Handle make current loading state
        } else if (state is EditAddressSuccess) {
          setState(() {
            makingCurrentAddressId = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
          context.read<AccountBloc>().add(ListAddressesEvent());
        } else if (state is EditAddressFailed) {
          setState(() {
            makingCurrentAddressId = null;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF6F3EC),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF6F3EC),
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              size: twentyFour,
              color: Color(0xFF1F2122),
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
        body: Padding(
          padding:
              EdgeInsets.only(bottom: sixteen, right: sixteen, left: sixteen),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Saved Address',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: eighteen,
                  height: 1.24,
                  letterSpacing: -1,
                  color: Color(0xFF1F2122),
                ),
              ),
              SizedBox(height: twentyFour),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(twelve),
                ),
                padding: EdgeInsets.all(sixteen),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isLoading)
                      const Center(
                        child: CircularProgressIndicator(),
                      )
                    else if (addresses == null || addresses!.isEmpty)
                      Center(
                        child: Text(
                          'No saved addresses',
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: sixteen,
                            color: Color(0xFF66696D),
                          ),
                        ),
                      )
                    else
                      ...addresses!.take(3).map((address) {
                        return Column(
                          children: [
                            BlocBuilder<AccountBloc, AccountState>(
                              buildWhen: (previous, current) {
                                return current is DeleteAddressLoading ||
                                    current is DeleteAddressSuccess ||
                                    current is DeleteAddressFailed ||
                                    current is EditAddressLoading ||
                                    current is EditAddressSuccess ||
                                    current is EditAddressFailed;
                              },
                              builder: (context, state) {
                                bool isDeleting =
                                    state is DeleteAddressLoading &&
                                        deletingAddressId == address.id;
                                bool isMakingCurrent =
                                    state is EditAddressLoading &&
                                        makingCurrentAddressId == address.id;

                                return _buildAddressCard(
                                  addressData: address,
                                  context: context,
                                  isDeleting: isDeleting,
                                  isMakingCurrent: isMakingCurrent,
                                );
                              },
                            ),
                            if (address != addresses!.take(3).last)
                              SizedBox(height: twentyFour),
                          ],
                        );
                      }).toList(),
                    if (!isLoading) ...[
                      SizedBox(height: sixteen),
                      Center(
                        child: OutlinedButton(
                          onPressed: () async {
                            final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const modify_address.ModifyAddressPage(
                                  type: "save",
                                ),
                              ),
                            );

                            // Reload addresses if a new address was added
                            if (result == true) {
                              context
                                  .read<AccountBloc>()
                                  .add(ListAddressesEvent());
                            }
                          },
                          style: OutlinedButton.styleFrom(
                            backgroundColor: Colors.white,
                            side: BorderSide(
                              color: Color(0xFF1F2122),
                              width: 1,
                            ),
                            minimumSize: Size(0, twentyFour * 1.3),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(twenty),
                            ),
                            padding: EdgeInsets.symmetric(
                                vertical: 0, horizontal: twenty),
                          ),
                          child: Text(
                            'Add new Address',
                            style: TextStyle(
                              fontFamily: 'Suisse Int\'l',
                              fontWeight: FontWeight.w400,
                              fontSize: twelve,
                              letterSpacing: 0.24,
                              color: Color(0xFF1F2122),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddressCard({
    required AddressData addressData,
    required BuildContext context,
    bool isDeleting = false,
    bool isMakingCurrent = false,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: const Color(0xFFF6F3EC),
        borderRadius: BorderRadius.circular(ten),
        border: Border.all(
          color: const Color(0xFFB9B6AD),
          width: 1,
        ),
      ),
      padding: EdgeInsets.all(twelve),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Address',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500,
                  fontSize: forteen,
                  height: sixteen / 14,
                  letterSpacing: 0.28,
                  color: Color(0xFF1F2122),
                ),
              ),
              if (addressData.isCurrent == true)
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: ten, vertical: sixteen / 4),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(twenty),
                    border: Border.all(
                      color: Colors.green.shade300,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    'Current',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      fontSize: ten,
                      color: Colors.green.shade700,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: forteen),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  Image.asset(
                    'assets/icons/location.png',
                    width: sixteen,
                    height: sixteen,
                  ),
                ],
              ),
              SizedBox(width: ten),
              // Address Text Column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _formatAddress(addressData),
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w500,
                        fontSize: forteen,
                        height: twenty / 12,
                        letterSpacing: 0,
                        color: Color(0xFF1F2122),
                      ),
                    ),
                    SizedBox(height: ten),
                    Row(
                      children: [
                        if (addressData.isCurrent != true)
                          GestureDetector(
                            onTap: isDeleting
                                ? null
                                : () async {
                                    final result =
                                        await ConfirmationDialog.show(
                                      context,
                                      title: 'Confirm',
                                      message:
                                          'Are you sure you want to delete this address?',
                                      cancelText: 'Cancel',
                                      confirmText: 'Delete',
                                    );

                                    if (result == true) {
                                      setState(() {
                                        deletingAddressId = addressData.id;
                                      });
                                      context.read<AccountBloc>().add(
                                            DeleteAddressEvent(addressData.id!),
                                          );
                                    }
                                  },
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (isDeleting)
                                  // SizedBox(
                                  //   width: twelve,
                                  //   height: twelve,
                                  //   child: CircularProgressIndicator(
                                  //     strokeWidth: 2,
                                  //     valueColor: AlwaysStoppedAnimation<Color>(
                                  //       Color(0xFF414346),
                                  //     ),
                                  //   ),
                                  // ),
                                  if (isDeleting) SizedBox(width: sixteen / 4),
                                Text(
                                  isDeleting ? 'Removing...' : 'Remove',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    fontSize: twelve,
                                    height: 1.0,
                                    letterSpacing: 0.24,
                                    decoration: isDeleting
                                        ? null
                                        : TextDecoration.underline,
                                    color: isDeleting
                                        ? Color(0xFF414346).withOpacity(0.6)
                                        : Color(0xFF414346),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        if (addressData.isCurrent != true)
                          SizedBox(width: sixteen),
                        if (addressData.isCurrent != true)
                          GestureDetector(
                            onTap: (isDeleting || isMakingCurrent)
                                ? null
                                : () async {
                                    final result =
                                        await ConfirmationDialog.show(
                                      context,
                                      title: 'Make Current Address',
                                      message:
                                          'Are you sure you want to make this your current address?',
                                      cancelText: 'Cancel',
                                      confirmText: 'Make Current',
                                    );

                                    if (result == true) {
                                      setState(() {
                                        makingCurrentAddressId = addressData.id;
                                      });
                                      context.read<AccountBloc>().add(
                                            EditAddressEvent({
                                              "id": addressData.id!,
                                              "latitude": addressData.location
                                                      ?.coordinates?[1] ??
                                                  0.0,
                                              "longitude": addressData.location
                                                      ?.coordinates?[0] ??
                                                  0.0,
                                              "address_text":
                                                  addressData.addressText ?? '',
                                              "building_type":
                                                  addressData.buildingType ??
                                                      'House',
                                              "house_number":
                                                  addressData.houseNumber ?? '',
                                              "city": addressData.city ?? '',
                                              "landmark":
                                                  addressData.landmark ?? '',
                                              "is_current": true,
                                            }),
                                          );
                                    }
                                  },
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (isMakingCurrent)
                                  SizedBox(
                                    width: twelve,
                                    height: twelve,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Color(0xFF414346),
                                      ),
                                    ),
                                  ),
                                if (isMakingCurrent)
                                  SizedBox(width: sixteen / 4),
                                Text(
                                  isMakingCurrent
                                      ? 'Making Current...'
                                      : 'Make Current',
                                  style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontWeight: FontWeight.w400,
                                    fontSize: twelve,
                                    height: 1.0,
                                    letterSpacing: 0.24,
                                    decoration: isMakingCurrent
                                        ? null
                                        : TextDecoration.underline,
                                    color: isMakingCurrent
                                        ? Color(0xFF414346).withOpacity(0.6)
                                        : Color(0xFF414346),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        if (addressData.isCurrent != true)
                          SizedBox(width: sixteen),
                        GestureDetector(
                          onTap: () async {
                            final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    modify_address.ModifyAddressPage(
                                  type: "edit",
                                  addressId: addressData.id,
                                ),
                              ),
                            );

                            if (result != null) {
                              context
                                  .read<AccountBloc>()
                                  .add(ListAddressesEvent());
                            }
                          },
                          child: Text(
                            'Edit',
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontWeight: FontWeight.w400,
                              fontSize: twelve,
                              height: 1.0,
                              letterSpacing: 0.24,
                              decoration: TextDecoration.underline,
                              color: Color(0xFF414346),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatAddress(AddressData addressData) {
    List<String> addressParts = [];

    if (addressData.houseNumber?.isNotEmpty == true) {
      addressParts.add(addressData.houseNumber!);
    }

    if (addressData.addressText?.isNotEmpty == true) {
      addressParts.add(addressData.addressText!);
    }

    if (addressData.buildingType?.isNotEmpty == true) {
      addressParts.add(addressData.buildingType!);
    }

    if (addressData.city?.isNotEmpty == true) {
      addressParts.add('${addressData.city}');
    }

    if (addressData.landmark?.isNotEmpty == true) {
      addressParts.add('${addressData.landmark}');
    }

    return addressParts.join('\n');
  }
}

class ConfirmationDialog extends StatefulWidget {
  final String title;
  final String message;
  final String cancelText;
  final String confirmText;
  final VoidCallback? onCancel;
  final VoidCallback? onConfirm;

  const ConfirmationDialog({
    super.key,
    this.title = 'Confirm',
    this.message = 'Are you sure you want to delete this address?',
    this.cancelText = 'Cancel',
    this.confirmText = 'Delete',
    this.onCancel,
    this.onConfirm,
  });
  // Static method to show the dialog
  static Future<bool?> show(
    BuildContext context, {
    String title = 'Confirm',
    String message = 'Are you sure you want to delete this address?',
    String cancelText = 'Cancel',
    String confirmText = 'Delete',
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return ConfirmationDialog(
          title: title,
          message: message,
          cancelText: cancelText,
          confirmText: confirmText,
          onCancel: () => Navigator.of(context).pop(false),
          onConfirm: () => Navigator.of(context).pop(true),
        );
      },
    );
  }

  @override
  State<ConfirmationDialog> createState() => _ConfirmationDialogState();
}

class _ConfirmationDialogState extends State<ConfirmationDialog> {
  late double ten;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    ten = screenWidth * 0.02545;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(sixteen),
      ),
      child: Container(
        padding: EdgeInsets.all(twenty),
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(width: twentyFour),
                Text(
                  widget.title,
                  style: TextStyle(
                    fontFamily: 'Suisse Int\'l',
                    fontWeight: FontWeight.w400,
                    fontSize: eighteen,
                    height: 1.28,
                    letterSpacing: 0,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Icon(
                    Icons.close,
                    size: twentyFour,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
            SizedBox(height: twelve),
            Text(
              widget.message,
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w400,
                fontSize: forteen,
                height: 1.43,
                letterSpacing: 0,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: ten * 1.6),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed:
                        widget.onCancel ?? () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      backgroundColor: Colors.white,
                      side: const BorderSide(color: Colors.black12, width: 1),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50),
                      ),
                      padding: EdgeInsets.symmetric(vertical: sixteen),
                    ),
                    child: Text(
                      widget.cancelText,
                      style: TextStyle(
                        fontFamily: 'Suisse Int\'l',
                        fontWeight: FontWeight.w400,
                        fontSize: sixteen,
                        height: 1.0,
                        letterSpacing: 0.32,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: sixteen),
                Expanded(
                  child: ElevatedButton(
                    onPressed:
                        widget.onConfirm ?? () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black87,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50),
                      ),
                      padding: EdgeInsets.symmetric(vertical: sixteen),
                    ),
                    child: Text(
                      widget.confirmText,
                      style: TextStyle(
                        fontFamily: 'Suisse Int\'l',
                        fontWeight: FontWeight.w400,
                        fontSize: sixteen,
                        height: 1.0,
                        letterSpacing: 0.32,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
